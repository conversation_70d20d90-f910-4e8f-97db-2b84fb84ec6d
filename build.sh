#!/usr/bin/env bash
# build.sh <envType>
# node-scripts 自动生成，请勿修改，并保证上传到 gitlab 上

set -e

BASE_DIR=${PWD}
echo "------------- build.sh start, pwd: ${PWD}, schema: ${SCHEMA_NAME}, envType: ${ENV_TYPE}, appname: ${APP_NAME}, baseDir: ${BASE_DIR} at $(date +%Y/%m/%d\ %H:%M:%S) ------------"

safe_remove_dir() {
  # safe_remove 命令不存在就使用 rm -rf 代替
  # safe_remove 只在 aone 环境存在
  if command -v safe_remove >/dev/null 2>&1; then
    safe_remove $@
  else
    rm -rf $@
  fi
}

if [ -d ./node_modules ]; then
  echo "[node-scripts] remove exist node_modules directory!"
  safe_remove_dir ./node_modules
fi

# 打印打包服务器的操作系统版本
cat /proc/version
ls -al /etc/*-release
cat /etc/*-release
uname -a

BEFORE_ALL=$(date +%s%3N)
if [ -f package.json ] && [ $(node -pe 'parseInt(process.version.slice(1))' 2>/dev/null || echo 0) -lt 20 ]; then
  NODE_VERSION=$(node -p "require('./package.json').engines['install-node']" 2>/dev/null || echo 20)

  if [ "$NODE_VERSION" = "20" ]; then
    nvm install node-v20.19.4
  else
    echo "Unsupported Node.js version: $NODE_VERSION and use node 20 instead"
    nvm install node-v20.19.4
  fi
fi

# 打印下 node 版本
echo "node version: $(node -v)"


NPM_TMP_GLOBAL_DIR=${BASE_DIR}/.npm_global
safe_remove_dir ${NPM_TMP_GLOBAL_DIR}
mkdir -p ${NPM_TMP_GLOBAL_DIR}/tmp

BEFORE_TNPM=$(date +%s%3N)
echo nvm:$((${BEFORE_TNPM} - ${BEFORE_ALL}))ms >> ${APP_NAME}.stepcost


# 禁用 tnpm 自动升级
export TNPM_UPDATER=false
TNPM_VERSION='latest-10'
INSTALL_DEPS_MODE="--force"

# 设置 xprofiler 源地址，防止 npm 模式下安装到 github 包导致报错
# 如果是包含 package-lock.json 的锁包情况，使用 export 环境变量，否则赋值当前变量
if [ -f package-lock.json ]; then
  export npm_config_xprofiler_binary_host_mirror="https://anpm.alibaba-inc.com/mirrors/xprofiler"
else
  npm_config_xprofiler_binary_host_mirror="https://anpm.alibaba-inc.com/mirrors/xprofiler"
fi

NPM_TMP_GLOBAL_DIR=$(npm prefix -g)

# https://yuque.antfin.com/frontbase/tnpm/install-at-ci
export PATH=${BASE_DIR}/node_modules/.bin:${BASE_DIR}/.node/bin:${BASE_DIR}/.bin:${NPM_TMP_GLOBAL_DIR}/bin:${NPM_TMP_GLOBAL_DIR}/lib/node_modules/tnpm/bin:/usr/local/gcc-5.2.0/bin:$PATH
curl -v -L --retry 5 --retry-delay 3 -o- https://anpm.alibaba-inc.com/open/install-tnpm-standalone.sh | bash -s ${TNPM_VERSION}

tnpm -v

BEFORE_CHECK=$(date +%s%3N)
echo tnpm:$((${BEFORE_CHECK} - ${BEFORE_TNPM}))ms >> ${APP_NAME}.stepcost

# http://gitlab.alibaba-inc.com/node/precompile-check/tree/master 不允许直接依赖的模块检查
mkdir precompile-check-tmp
cp package.json precompile-check-tmp
cd precompile-check-tmp
tnpm i @ali/precompile-check ${INSTALL_DEPS_MODE}
node node_modules/@ali/precompile-check/index.js
cd ..

safe_remove_dir precompile-check-tmp

BEFORE_INSTALL=$(date +%s%3N)
echo precompile_check:$((${BEFORE_INSTALL} - ${BEFORE_CHECK}))ms >> ${APP_NAME}.stepcost

time tnpm i ${INSTALL_DEPS_MODE} --aone_app_pwd=${BASE_DIR} --aone_env_type=${ENV_TYPE} --aone_app_name=${APP_NAME} --aone_schema=${SCHEMA_NAME} || exit $?

BEFORE_PROXY=$(date +%s%3N)
echo install:$((${BEFORE_PROXY} - ${BEFORE_INSTALL}))ms >> ${APP_NAME}.stepcost

# 尝试执行 tnpm run autoproxy 自动生成 proxy 代码
echo "[node-scripts] try tnpm run --if-present autoproxy... at $(date +%Y/%m/%d\ %H:%M:%S) "
AUTOPROXY_ERROR=0
AUTOPROXY_OUTPUT=`tnpm run --if-present autoproxy 2>&1` || AUTOPROXY_ERROR=1
if [[ ${AUTOPROXY_ERROR} -eq 0 ]]
  then
  echo "${AUTOPROXY_OUTPUT}"
  echo "[node-scripts] autoproxy success!"
else
  echo "${AUTOPROXY_OUTPUT}"
  echo "[node-scripts] autoproxy error! please check your npm autoproxy script! at $(date +%Y/%m/%d\ %H:%M:%S) "
  exit 1
fi

BEFORE_CUSTOM=$(date +%s%3N)
echo autoproxy:$((${BEFORE_CUSTOM} - ${BEFORE_PROXY}))ms >> ${APP_NAME}.stepcost

# # 执行 tnpm run build 来进行应用自定义的构建
# echo "[node-scripts] start custom build: tnpm run --if-present build... at $(date +%Y/%m/%d\ %H:%M:%S) "

# # 临时修正下面 /home/<USER>/tmp 不存在时 mktemp 报错的问题
# if ! [ -d /home/<USER>/tmp ]; then
#   mkdir -p /home/<USER>/tmp
# fi

# BUILD_RESULT_TEMP=`mktemp`
# export BUILD_RESULT_TEMP
# BUILD_OUTPUT_TEMP=`mktemp`
# BUILD_ERROR=0

# # subshell, 通过临时文件传递执行结果
# # (tnpm run build 2>&1 ||: ; echo "$?" > $BUILD_RESULT_TEMP) | tee $BUILD_OUTPUT_TEMP
# (BUILD_ERROR=0;echo $BUILD_RESULT_TEMP; tnpm run --if-present build 2>&1 || BUILD_ERROR=1; echo $BUILD_ERROR > $BUILD_RESULT_TEMP) | tee $BUILD_OUTPUT_TEMP

# AFTER_CUSTOM=$(date +%s%3N)
# echo custom_script:$((${AFTER_CUSTOM} - ${BEFORE_CUSTOM}))ms >> ${APP_NAME}.stepcost

# BUILD_ERROR=`cat $BUILD_RESULT_TEMP`
# BUILD_OUTPUT=`cat $BUILD_OUTPUT_TEMP`

# echo "[node-scripts] custom build return code: $BUILD_ERROR"

# 设置环境变量禁用 miniflare 以避免 EPIPE 错误
export DISABLE_MINIFLARE=true
export NODE_ENV=production

tnpm run build || return 1

if [[ $BUILD_ERROR -eq 0 ]]
then
  echo "[node-scripts] custom build success!"
else
  echo "[node-scripts] custom build error! please check your npm build script! at $(date +%Y/%m/%d\ %H:%M:%S) "
  exit 1
fi

echo "[node-scripts] build success! at $(date +%Y/%m/%d\ %H:%M:%S) "

# 删除安装的devDeps依赖, 重新安装线上依赖
if [ -z ${INSTALL_DEPS_MODE} ]
  then
  safe_remove_dir node_modules
  # 安装线上依赖
  time tnpm i --production --aone_app_pwd=${BASE_DIR} --aone_env_type=${ENV_TYPE} --aone_app_name=${APP_NAME} --aone_schema=${SCHEMA_NAME} || exit $?
fi

# http://gitlab.alibaba-inc.com/node/sigma/issues/26488 解决 CPU Share 化后 egg 自动获取到的核心数太多的问题
tnpm i @ali/sigma --production

# echo "[node-scripts] reporting package.json to npm.alibaba-inc.com ... at $(date +%Y/%m/%d\ %H:%M:%S) "
# tnpm i @ali/node-app-reporter@1
# # $ node-app-reporter [baseDir] [appname] [buildId] [buildAuthor]
# node-app-reporter ${BASE_DIR} ${APP_NAME} ${ENV_TYPE}
# echo "[node-scripts] report success! at $(date +%Y/%m/%d\ %H:%M:%S) "


AFTER_ALL=$(date +%s%3N)
echo finalize:$((${AFTER_ALL} - ${AFTER_CUSTOM}))ms >> ${APP_NAME}.stepcost

echo "step_cost:"
cat ${APP_NAME}.stepcost
