# 更多关于Release文件的规范和约定，请点击 https://yuque.antfin.com/aone/geh0qs/252891532
# Aone3 必须的项目配置文件。由 node-scripts 自动生成，请勿修改，并保证上传到 gitlab 上。
code.language=nodejs
build.tools.docker.args=--build-arg APP_NAME=${APP_NAME} --build-arg ENV_TYPE=${ENV_TYPE}

# 跳过切换 nodejs 版本，True 时为跳过
build.plugin.skip.swnode=True

# 跳过 nodejs 应用 precompile 检查，True 时为跳过
build.plugin.skip.npcc=True

# 在构建机上使用 AliOS 8 镜像编译
build.image=hub.docker.alibaba-inc.com/aone-base-global/alios8-nodejs:builder
# 可能有旧版本的缓存，如果生成 proxy 期间报错，请换成下面这个
# build.image=hub.docker.alibaba-inc.com/aone-base-global/alios8-nodejs:20250403105553

# 默认使用 Java 8，避免很多新的 jar 包无法通过 jar2proxy 生成 js proxy 文件
baseline.jdk=jdk-1.8.0_40
