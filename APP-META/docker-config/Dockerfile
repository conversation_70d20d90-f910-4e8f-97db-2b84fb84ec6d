FROM hub.docker.alibaba-inc.com/aone-base-global/alios8-nodejs:2.0

EXPOSE 80
EXPOSE 6001
# 安装 bind-utils 解决 nslookup 命令缺失问题
RUN yum install -y bind-utils

# 设置应用名和 Node.js 版本变量

ENV APP_NAME=dd-ai-builder
ENV NODE_VERSION=20.19.4
ENV PATH="/home/<USER>/${APP_NAME}/target/${APP_NAME}/.node/node-v${NODE_VERSION}/bin:${PATH}:/home/<USER>/${APP_NAME}/target/${APP_NAME}/node_modules/.bin"


RUN mkdir -p /home/<USER>/dd-ai-builder/target/ && \
cp -rf /home/<USER>/appname/bin /home/<USER>/dd-ai-builder

WORKDIR /home/<USER>/dd-ai-builder/bin

VOLUME /home/<USER>/logs /home/<USER>/cai/logs /home/<USER>/dd-ai-builder/logs /home/<USER>/vipsrv-dns/vipsrv-cache

COPY environment/nginx/* /home/<USER>/cai/conf/
COPY environment/scripts/*.sh /home/<USER>/

RUN chmod +x /home/<USER>/*.sh


RUN echo "/home/<USER>/dd-ai-builder/bin/nodejsctl pubstart" >> /home/<USER>/start.sh
ENTRYPOINT ["/home/<USER>/start.sh"]
COPY dd-ai-builder.tgz /home/<USER>/dd-ai-builder/target/dd-ai-builder.tgz
