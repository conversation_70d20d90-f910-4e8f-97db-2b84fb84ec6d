# proxy conf
user                        admin;

worker_rlimit_nofile        100000;

error_log                   "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-error_log" warn;
pid                         logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}

# dso
# dso {
#    load ngx_http_tmd_module.so;
#    load ngx_http_beacon_filter_module.so;
# }

http {
    include                 mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    # tcp_nopush              on;  # 禁用以优化 SSE 实时性

    server_tokens           off;

    keepalive_timeout       65s;

    client_header_timeout   1m;
    send_timeout            10m;
    # don't change the default value, it will be auto override by the node-scripts on aone build process
    client_max_body_size    3m;
    client_body_temp_path   data/client_body;

    # error_page              400 http://err.taobao.com/error1.html;
    # error_page              403 http://err.taobao.com/error1.html;
    # error_page              404 http://err.taobao.com/error1.html;
    # error_page              405 http://err.taobao.com/error1.html;
    # error_page              408 http://err.taobao.com/error1.html;
    # error_page              410 http://err.taobao.com/error1.html;
    # error_page              411 http://err.taobao.com/error1.html;
    # error_page              412 http://err.taobao.com/error1.html;
    # error_page              413 http://err.taobao.com/error1.html;
    # error_page              414 http://err.taobao.com/error1.html;
    # error_page              415 http://err.taobao.com/error1.html;
    #error_page              500 http://err.taobao.com/error2.html;
    #error_page              501 http://err.taobao.com/error2.html;
    #error_page              502 http://err.taobao.com/error2.html;
    #error_page              503 http://err.taobao.com/error2.html;
    #error_page              506 http://err.taobao.com/error2.html;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $request_time_usec $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\" \"$eagleeye_traceid\"";

    access_log              "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-taobao-access_log" proxyformat;
    log_not_found           off;

    #gzip                    on;
    #gzip_http_version       1.0;
    #gzip_comp_level         6;
    #gzip_min_length         1024;
    #gzip_proxied            any;
    #gzip_vary               on;
    #gzip_disable            msie6;
    #gzip_buffers            96 8k;
    #gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    #beacon                  on;
    #beacon_cfg              taobao-beacon.cfg taobao-channel.cfg;

    # taobao trans
    #trans                   off;
    #trans_cookie_name       _lang;
    #trans_cookie_trans_value zh_CN:TB-GBK;
    #trans_cookie_nottrans_value zh_CN:GBK;
    #trans_ip_file           ip.dat;
    #trans_code_file         sm2tr.txt;
    #trans_content_type      application/xhtml+xml text/plain text/css text/xml text/javascript;
    #trans_accept_language_trans zh-HK zh-TW zh-MO zh-Hant;
    #trans_accept_language_notrans zh-CN zh-SG zh-Hans;

    eagleeye_traceid_var    $eagleeye_traceid;
    #eagleeye_traceid_arg   tb_eagleeye_traceid;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        EagleEye-TraceId $eagleeye_traceid;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  on;

    # fight mhtml/utf-7 bug
    hat_content             "\r\n";
    hat_types               text/html text/css;

    upstream nodejs {
      server 127.0.0.1:6001;
      keepalive 32;
    }

    server {
        listen              80 default_server;
        server_name         www.taobao.com;

        # SSE/流式接口专用配置（HTTP/2 优化版）
        location ~* ^/api/(chat|enhancer|llmcall)$ {
            proxy_pass http://nodejs;

            # === HTTP/2 + SSE 核心配置 ===
            # 禁用所有缓冲以确保实时传输
            proxy_buffering off;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        # 其他API接口的默认配置
        location / {
            proxy_pass http://nodejs;
        }
    }

    server {
        listen              80;
        server_name         status.taobao.com;

        tmd off;

        location            = /nginx_status {
            stub_status     on;
        }
    }

    include apps/*.conf;
}
