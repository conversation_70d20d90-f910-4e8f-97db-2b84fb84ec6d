# AI API Keys - Required for AI functionality
OPENAI_API_KEY=sk-xxxxx
ANTHROPIC_API_KEY=sk-ant-xxxxx
GOOGLE_GENERATIVE_AI_API_KEY=xxxxx
GROQ_API_KEY=gsk_xxxxx
XAI_API_KEY=xai-xxxxx
TOGETHER_API_KEY=xxxxx

# Optional API Keys
HUGGINGFACE_API_KEY=hf_xxxxx
COHERE_API_KEY=xxxxx
MISTRAL_API_KEY=xxxxx
PERPLEXITY_API_KEY=pplx-xxxxx
OPEN_ROUTER_API_KEY=sk-or-xxxxx

# API Base URLs (Optional - for custom endpoints)
OPENAI_LIKE_API_BASE_URL=https://api.openai.com/v1
OLLAMA_API_BASE_URL=http://localhost:11434
LMSTUDIO_API_BASE_URL=http://localhost:1234/v1
TOGETHER_API_BASE_URL=https://api.together.xyz/v1

# AWS Bedrock Configuration (Optional)
# AWS_BEDROCK_CONFIG={"region":"us-east-1","accessKeyId":"xxx","secretAccessKey":"xxx"}

# Application Settings
NODE_ENV=production
PORT=3000

# Logging and Debug
VITE_LOG_LEVEL=info
DEFAULT_NUM_CTX=32768

# Deployment Environment
RUNNING_IN_DOCKER=false
