#!/bin/bash

# 替换 @webcontainer/api 为 webc-api 的脚本

echo "开始替换 @webcontainer/api 导入..."

# 查找所有包含 @webcontainer/api 的文件
find app -name "*.ts" -o -name "*.tsx" | while read -r file; do
  if grep -q "@webcontainer/api" "$file"; then
    echo "处理文件: $file"

    # 替换导入语句
    sed -i '' 's|from '\''@webcontainer/api'\''|from '\''~/lib/webc-api'\''|g' "$file"
    sed -i '' 's|import.*@webcontainer/api.*|import { WebContainer, WebContainerProcess, PathWatcherEvent, TextSearchOptions, TextSearchOnProgressCallback } from '\''~/lib/webc-api'\'';|g' "$file"

    echo "已更新: $file"
  fi
done

echo "替换完成！"
