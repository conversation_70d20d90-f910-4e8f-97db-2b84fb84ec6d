#!/usr/bin/env node

import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const projectRoot = join(__dirname, '..');
const nodeModulesPath = join(projectRoot, 'node_modules');
const sourcePath = join(nodeModulesPath, '@ali/wasm-webc-sys', 'lib/webc_service_worker.js');
const targetPath = join(projectRoot, 'public', 'webc_service_worker.js');

try {
  // 检查源文件是否存在
  if (!existsSync(sourcePath)) {
    console.log('⚠️  Source file not found:', sourcePath);
    console.log('   This might be normal if @ali/wasm-webc-sys is not installed yet.');
    process.exit(0);
  }

  // 确保目标目录存在
  const targetDir = dirname(targetPath);
  if (!existsSync(targetDir)) {
    mkdirSync(targetDir, { recursive: true });
  }

  // 读取源文件
  const content = readFileSync(sourcePath, 'utf8');

  // 写入目标文件
  writeFileSync(targetPath, content, 'utf8');

  console.log('✅  Successfully copied webc_service_worker.js to public/');
  console.log('   Source:', sourcePath);
  console.log('   Target:', targetPath);
} catch (error) {
  console.error('❌  Error copying service worker file:', error.message);
  process.exit(1);
}
