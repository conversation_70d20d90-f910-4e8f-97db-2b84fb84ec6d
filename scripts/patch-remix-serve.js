#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

/**
 * Remix Serve 补丁脚本 - 替换 @remix-run/serve 的 cli.js
 * 用项目中的 fix-remix-serve.js 替换 node_modules/@remix-run/serve/dist/cli.js
 */

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const sourceFile = path.join(__dirname, 'fix-remix-serve.js');
const targetFile = path.join(__dirname, '..', 'node_modules', '@remix-run', 'serve', 'dist', 'cli.js');

function patchRemixServe() {
  try {
    // 检查源文件是否存在
    if (!fs.existsSync(sourceFile)) {
      console.error('❌ 补丁源文件不存在:', sourceFile);
      process.exit(1);
    }

    // 检查目标目录是否存在
    const targetDir = path.dirname(targetFile);
    if (!fs.existsSync(targetDir)) {
      console.log('⚠️  目标目录不存在，可能 @remix-run/serve 未安装:', targetDir);
      return;
    }

    // 备份原文件（如果存在）
    if (fs.existsSync(targetFile)) {
      const backupFile = targetFile + '.backup';
      if (!fs.existsSync(backupFile)) {
        fs.copyFileSync(targetFile, backupFile);
        console.log('📦 已备份原文件为:', backupFile);
      }
    }

    // 复制修复的文件
    fs.copyFileSync(sourceFile, targetFile);

    // 确保文件有执行权限
    fs.chmodSync(targetFile, '755');

    console.log('✅ 成功应用 remix-serve 补丁');
    console.log('   补丁文件:', sourceFile);
    console.log('   目标文件:', targetFile);

  } catch (error) {
    console.error('❌ 应用 remix-serve 补丁失败:', error.message);
    process.exit(1);
  }
}

// 执行补丁
console.log('🔧 开始应用 remix-serve 补丁...');
patchRemixServe();
console.log('✨ remix-serve 补丁应用完成');
