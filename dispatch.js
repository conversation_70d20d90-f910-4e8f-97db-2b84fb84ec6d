#!/usr/bin/env node

import { spawn } from 'child_process';
import path from 'path';
import fs from 'fs';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取项目根目录
const projectRoot = path.resolve(__dirname, '.');

// 加载 .env.production 环境变量
function loadProductionEnv() {
  const envPath = path.join(projectRoot, '.env.production');

  if (fs.existsSync(envPath)) {
    try {
      const result = dotenv.config({ path: envPath });
      if (!result.error) {
        console.log('🔧 Loaded .env.production');
        console.log(result.parsed);
      } else {
        console.warn('⚠️  Failed to parse .env.production:', result.error.message);
      }
    } catch (error) {
      console.warn('⚠️  Failed to load .env.production:', error.message);
    }
  } else {
    console.log('ℹ️  .env.production file not found');
  }
}

// 检查 package.json 是否存在
const packageJsonPath = path.join(projectRoot, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ package.json not found in project root');
  process.exit(1);
}

// 加载生产环境变量
loadProductionEnv();

// 检查 start 脚本是否存在
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
if (!packageJson.scripts || !packageJson.scripts.start) {
  console.error('❌ start script not found in package.json');
  process.exit(1);
}

console.log('🚀 Starting server...');
console.log(`📁 Project root: ${projectRoot}`);
console.log(`📦 Package manager: ${packageJson.packageManager || 'npm'}`);
console.log(`🌍 NODE_ENV: ${process.env.NODE_ENV || 'production'}`);

// 根据 packageManager 选择命令
let command, args;
if (packageJson.packageManager && packageJson.packageManager.startsWith('tnpm')) {
  command = 'tnpm';
  args = ['start'];
} else if (packageJson.packageManager && packageJson.packageManager.startsWith('pnpm')) {
  command = 'pnpm';
  args = ['start'];
} else {
  command = 'npm';
  args = ['start'];
}

console.log(`⚡ Executing: ${command} ${args.join(' ')}`);

// 启动子进程
const child = spawn(command, args, {
  cwd: projectRoot,
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: process.env.NODE_ENV || 'production',
  },
});

// 处理子进程事件
child.on('error', (error) => {
  console.error('❌ Failed to start server:', error.message);
  process.exit(1);
});

child.on('exit', (code, signal) => {
  if (signal) {
    console.log(`🛑 Server stopped by signal: ${signal}`);
  } else {
    console.log(`✅ Server exited with code: ${code}`);
  }
  process.exit(code || 0);
});

// 处理进程信号
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down...');
  child.kill('SIGINT');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down...');
  child.kill('SIGTERM');
});

// 优雅关闭
process.on('beforeExit', () => {
  console.log('🛑 Server shutting down gracefully...');
  child.kill();
});
