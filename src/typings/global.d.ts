/* eslint-disable @typescript-eslint/no-explicit-any */
/**
 * Global type declarations for Next.js and webpack runtime
 */

declare global {
  interface Window {
    // Next.js webpack runtime chunk loading
    webpackChunk_N_E?: any[];
    __webpack_require__?: any;
    
    // Next.js runtime globals
    __NEXT_DATA__?: any;
    __NEXT_LOADED_PAGES__?: any[];
    __NEXT_P?: any[];
    
    // Other potential webpack/Next.js globals
    webpackHotUpdate?: any;
    webpackJsonp?: any[];
  }
}

// This export is necessary to make this a module
export {};
