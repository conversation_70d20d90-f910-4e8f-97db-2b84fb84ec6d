.video-form {
  min-height: calc(100vh - 106px);
  background-color: #1e1e1f;
  padding: 16px;
  // Add iOS safe area support for bottom padding
  padding-bottom: calc(env(safe-area-inset-bottom) + 16px);

  .form-container {
    margin: 0 auto;
    background: transparent;
    padding: 0;

    .form-section {
      margin-bottom: 12px;

      .form-title {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        gap: 6px;

        .form-title-text {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 24px;
          font-weight: 600;
        }

        .example-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          background: none;
          border: 1px solid #FF0E53;
          color: #FF0E53;
          padding: 3px 6px;
          border-radius: 40px;
          white-space: nowrap;
          font-size: 12px;
          font-weight: 500;
          line-height: 18px;
          cursor: pointer;
          transition: all 0.2s ease;

          svg {
            width: 16px;
            height: 16px;
            margin-right: 2px;
          }

          &:hover {
            background: rgba(255, 14, 83, 0.1);
          }
        }
      }

      .textarea-container {
        position: relative;
        font-size: 0;

        .custom-textarea {
          width: 100%;
          min-height: 132px;
          background: #141414;
          border: 1px solid #141414;
          border-radius: 16px;
          padding: 18px 16px;
          color: #ffffff;
          font-size: 16px;
          line-height: 22px;
          resize: none; // Disable manual resize handle on PC
          outline: none;

          &::placeholder {
            color: rgba(255, 255, 255, 0.5);
          }

          // &:hover {
          //   border: 1px solid #FF0E53;
          // }

          &:active {
            border: 1px solid #FF0E53;
          }

          // Focus state for accessibility and auto-focus
          &:focus {
            border: 1px solid #FF0E53;
          }

          // // Focus-visible for better accessibility (only show focus ring when navigating with keyboard)
          // &:focus-visible {
          //   border: 1px solid #FF0E53;
          // }
        }

        .char-count {
          position: absolute;
          bottom: 8px;
          right: 12px;
          color: rgba(255, 255, 255, 0.5);
          font-size: 12px;
        }
      }

      .quality-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;

        .quality-label {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 22px;
          font-weight: 600;
        }

        .quality-toggle-container {
          .quality-toggle {
            position: relative;
            display: flex;
            background: #141414;
            border-radius: 19px;
            padding: 3px;
            width: 150px;
            height: 38px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            overflow: hidden;
            cursor: pointer;
            transition: all 0.2s ease;

            // Add subtle glow effect
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: #141414;
              border-radius: 9px;
              pointer-events: none;
            }

            .quality-slider {
              position: absolute;
              top: 3px;
              left: 3px;
              width: 71px;
              height: 30px;
              background: rgba(255, 255, 255, 0.24);
              border-radius: 17px;
              transition: transform 0.35s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              z-index: 1;

              &.slide-left {
                transform: translateX(0);
              }

              &.slide-right {
                transform: translateX(71px);
              }
            }

            .quality-option {
              position: relative;
              flex: 1;
              background: transparent;
              border: none;
              color: rgba(255, 255, 255, 0.9);
              font-size: 14px;
              cursor: pointer;
              z-index: 2;
              transition: all 0.3s ease;
              border-radius: 17px;
              display: flex;
              align-items: center;
              justify-content: center;
              user-select: none;
              -webkit-tap-highlight-color: transparent;

              &.active {
                color: #ffffff;
                font-weight: 600;
              }

              &:hover:not(.active) {
                color: rgba(255, 255, 255, 0.9);
              }

              &:active {
                transform: scale(0.98);
              }
            }

            // Add ripple effect on touch
            &:active {
              .quality-slider {
                transform: scale(0.98) translateX(var(--slider-position, 0));
              }
            }
          }
        }
      }

      .duration-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;

        .duration-label {
          color: rgba(255, 255, 255, 0.9);
          font-size: 16px;
          line-height: 22px;
          font-weight: 600;
        }

        .duration-value {
          color: #ffffff;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          padding-right: 2px;
        }
      }
    }

    .error-message {
      background: rgba(255, 77, 79, 0.1);
      border: 1px solid rgba(255, 77, 79, 0.3);
      border-radius: 8px;
      padding: 12px 16px;
      color: #ff4d4f;
      font-size: 14px;
    }

    // Quota Information Styles
    .quota-info {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 16px;
      gap: 8px;

      .quota-item {
        display: flex;
        align-items: center;
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        font-weight: 500;
        line-height: 18px;

        &.quota-zero {
          color: #FF0E53;
        }
      }

      .quota-divider {
        width: 0.5px;
        height: 12px;
        opacity: 0.3;
        background: #FFFFFF;
      }
    }

    .form-actions {
      padding-bottom: 24px;

      .generate-btn {
        width: 100%;
        height: 56px;
        line-height: 56px;
        border-radius: 28px;
        font-size: 24px;
        font-weight: 500;
        background: #ffffff;
        border: none;
        color: #141414;
        cursor: pointer;

        &:hover {
          background: rgba(255, 255, 255, 0.9);
          color: #000000;
        }

        &:active {
          background: rgba(255, 255, 255, 0.8);
          color: #000000;
        }

        &.dtm-button-disabled {
          background: #979797;
          color: #666666;
          cursor: not-allowed;

          &:hover {
            background: #979797;
            color: #666666;
          }

          &:active {
            background: #979797;
            color: #666666;
          }
        }
      }
    }
  }

  // Help content styles
  .help-drawer-content {
    padding: 16px;
    // Add iOS safe area support for bottom padding
    padding-bottom: calc(env(safe-area-inset-bottom) + 16px);
    overflow-y: auto;

    .help-content-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .help-text {
        color: #333333;
        font-size: 14px;
        line-height: 20px;
        margin: 0;
      }

      .help-content-image {
        width: 100%;
        max-width: 300px;
        height: auto;
        border-radius: 6px;
        margin: 6px 0;
        display: block;
      }

      // Section styles for help content (Mobile drawer)
      .help-section {
        .help-section-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          margin: 0 0 8px 0;
        }

        .help-section-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 22px;
        }

        .help-section-list {
          margin: 0;
          list-style: none;

          .help-section-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 22px;
            margin-bottom: 4px;
            position: relative;
          }
        }
      }

      // Examples styles for help content (Mobile drawer)
      .help-examples {
        margin: 16px 0 0;

        .help-examples-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .help-examples-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 8px;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }

          .help-examples-title {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin: 0;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }
        }

        .help-examples-grid {
          display: grid;
          gap: 18px;

          // Default: 2 columns for image upload examples (图片规范)
          grid-template-columns: repeat(2, 1fr);

          // Single column for prompt examples (创意描述参考)
          &.single-column {
            grid-template-columns: 1fr;
          }

          // Force two columns when isTwoImage is true
          &.force-two-columns {
            grid-template-columns: repeat(2, 1fr);
          }

          // Responsive design for mobile
          @media (max-width: 480px) {
            gap: 12px;

            // Force single column on very small screens, except when isTwoImage is true
            &:not(.single-column):not(.force-two-columns) {
              grid-template-columns: 1fr;
            }

            // Keep two columns for force-two-columns class even on mobile
            &.force-two-columns {
              grid-template-columns: repeat(2, 1fr);
            }
          }

          .help-example-item {
            text-align: center;
            position: relative;

            .help-example-copy-container {
              position: absolute;
              top: 8px;
              right: 8px;
              z-index: 10;

              .help-example-copy-btn {
                background: rgba(0, 0, 0, 0.6);
                border: none;
                padding: 6px;
                font-size: 18px;
                color: rgba(255, 255, 255, 0.9);
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                cursor: pointer;
                backdrop-filter: blur(4px);

                svg {
                  width: 16px !important;
                  height: 16px !important;
                }

                &:hover {
                  background: rgba(0, 0, 0, 0.8);
                  transform: scale(1.05);
                }

                &:active {
                  transform: scale(0.95);
                }
              }
            }

            .help-example-image-placeholder {
              width: 100%;
              background-color: transparent;
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              position: relative;

              .placeholder-content {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.5);
              }

              .help-example-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .help-example-label-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
                padding: 20px 12px 12px 12px;
                border-radius: 0 0 16px 16px;

                .help-example-label {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 20px;
                  text-align: center;
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Help popover styles for PC
.form-help-popover {
  &.dtd-popover {
    padding: 0;
    margin: 16px 0;
    max-height: 100vh;
    background-color: #1E1E1F;
    color: #fff;
    border-radius: 32px;
    overflow-y: auto;
  }

  .dtd-popover-inner {
    background: transparent;
    border-radius: 32px;
    padding: 0;
    max-width: 412px;
  }

  .dtd-popover-title {
    padding: 20px 16px 16px 16px;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    color: rgba(255, 255, 255, 0.9);
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.24);
    margin: 0;
  }

  .dtd-popover-inner-content {
    background: transparent;
    color: rgba(255, 255, 255, 0.9);
  }

  .dtd-popover-content {
    padding: 0;
  }

  .help-popover-content {
    padding: 0;
    overflow-y: auto;

    .help-content-item {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      .help-text {
        color: rgba(255, 255, 255, 0.9);
        font-size: 14px;
        line-height: 20px;
        margin: 0;
      }

      .help-content-image {
        width: 100%;
        max-width: 300px;
        height: auto;
        border-radius: 6px;
        margin: 6px 0;
        display: block;
      }

      // Section styles for help content (PC popover)
      .help-section {
        .help-section-title {
          font-size: 16px;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          margin: 0 0 8px 0;
        }

        .help-section-description {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.9);
          line-height: 22px;
        }

        .help-section-list {
          margin: 0;
          list-style: none;

          .help-section-item {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            line-height: 22px;
            margin-bottom: 4px;
            position: relative;
          }
        }
      }

      // Examples styles for help content (PC popover)
      .help-examples {
        margin: 16px 0 0;

        .help-examples-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .help-examples-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            margin-right: 8px;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }

          .help-examples-title {
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin: 0;

            &.correct {
              color: rgba(85, 255, 201);
            }

            &.incorrect {
              color: #FF0E53;
            }
          }
        }

        .help-examples-grid {
          display: grid;
          gap: 18px;

          // Default: 2 columns for image upload examples (图片规范)
          grid-template-columns: repeat(2, 1fr);

          // Single column for prompt examples (创意描述参考)
          &.single-column {
            grid-template-columns: 1fr;
          }

          // Force two columns when isTwoImage is true (PC端保持默认行为)
          &.force-two-columns {
            grid-template-columns: repeat(2, 1fr);
          }

          .help-example-item {
            text-align: center;
            position: relative;

            .help-example-copy-container {
              position: absolute;
              top: 8px;
              right: 8px;
              z-index: 10;

              .help-example-copy-btn {
                background: rgba(0, 0, 0, 0.6);
                border: none;
                padding: 6px;
                font-size: 18px;
                color: rgba(255, 255, 255, 0.9);
                border-radius: 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.2s ease;
                cursor: pointer;
                backdrop-filter: blur(4px);

                svg {
                  width: 16px !important;
                  height: 16px !important;
                }

                &:hover {
                  background: rgba(0, 0, 0, 0.8);
                  transform: scale(1.05);
                }

                &:active {
                  transform: scale(0.95);
                }
              }
            }

            .help-example-image-placeholder {
              width: 100%;
              background-color: transparent;
              border-radius: 16px;
              display: flex;
              align-items: center;
              justify-content: center;
              overflow: hidden;
              position: relative;

              .placeholder-content {
                font-size: 11px;
                color: rgba(255, 255, 255, 0.5);
              }

              .help-example-image {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .help-example-label-overlay {
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 100%);
                padding: 20px 12px 12px 12px;
                border-radius: 0 0 16px 16px;

                .help-example-label {
                  color: rgba(255, 255, 255, 0.9);
                  font-size: 14px;
                  font-weight: 500;
                  line-height: 20px;
                  text-align: center;
                  margin: 0;
                }
              }
            }
          }
        }
      }
    }
  }
}
