import { i18next } from '@ali/dingtalk-i18n';
import React, { useEffect, useState } from 'react';
import { openDualLink, isDingTalk, setPageTitle, isMobileDevice } from '@/utils/jsapi';
import { getAIImageUrl, getAIVideoUrl, getOrderListUrl } from '@/utils/env';
import { PictureOutlined, RightArrowLOutlined, VideoPlayOutlined } from '@ali/ding-icons';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { sendUT } from '@/utils/trace';

import FreeModal from '@/components/FreeModal';
import MobileNavbar from '@/components/MobileNavbar';
import FeaturePromotion from '@/components/FeaturePromotion';
import PcHeaderActions from '@/components/PcHeaderActions';
import useQuotaUsage from '@/hooks/useQuotaUsage';
import useFreeQuota from '@/hooks/useFreeQuota';
import './index.less';

const AIStudioPage: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);

  // 使用 useQuotaUsage hook 管理额度数据
  const { quotaUsage, refreshQuotaUsage } = useQuotaUsage('ai_material');

  // 使用 useFreeQuota hook 管理免费额度
  const {
    freeModalVisible,
    quotaAccessData,
    freeModalLoading,
    checkFreeQuota,
    handleFreeModalClose,
    handleFreeModalReceive: originalHandleFreeModalReceive,
  } = useFreeQuota({
    resourceKey: 'ai_material',
    eventPrefix: 'aigc_homepage',
    errorMessages: {
      obtainFailed: i18next.t('j-dingtalk-web_pages_ai-studio_FailedToObtainFreeQuota'),
      interfaceAbnormal: i18next.t('j-dingtalk-web_pages_ai-studio_TheInterfaceIsAbnormalPlease'),
      collectFailed: i18next.t('j-dingtalk-web_pages_ai-studio_FailedToCollectPleaseTry'),
    },
  });

  // 包装 handleFreeModalReceive 来传递 refreshQuotaUsage 函数
  const handleFreeModalReceive = async () => {
    await originalHandleFreeModalReceive(refreshQuotaUsage);
  };

  // 初始化页面设置
  useEffect(() => {
    // 检测设备类型
    setIsMobile(isMobileDevice());

    // 检查免费额度访问权限
    checkFreeQuota();

    // 设置页面标题
    setPageTitle(i18next.t('j-dingtalk-web_pages_ai-studio_AiMaterialDesign'));
    // 数据埋点上报
    sendUT('aigc_homepage_exposure', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });
  }, []);

  // 处理订单列表按钮点击事件
  const handleOrderListClick = () => {
    // Data tracking for order list click
    sendUT('aigc_homepage_order_list_click', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    const url = getOrderListUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      window.open(url);
    }
  };

  // 处理 AI 画像生成点击事件
  const handleAIImageClick = () => {
    sendUT('aigc_homepage_picture_click', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    const url = getAIImageUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      // 在浏览器环境中直接跳转
      window.location.href = url;
    }
  };

  // 处理 AI 视频生成点击事件
  const handleAIVideoClick = () => {
    sendUT('aigc_homepage_video_click', {
      device: isMobileDevice() ? 'mobile' : 'pc',
    });

    const url = getAIVideoUrl();
    if (isDingTalk()) {
      $openLink({
        url: openDualLink(url),
      });
    } else {
      // 在浏览器环境中直接跳转
      window.location.href = url;
    }
  };



  return (
    <>
      {/* Mobile Navigation Bar - Fixed at the top, outside of the main container */}
      {isMobile && (
        <MobileNavbar
          showOrderButton
          showShareButton
          onOrderClick={handleOrderListClick}
          shareConfig={{
            url: window.location.href,
            title: i18next.t('j-dingtalk-web_pages_ai-studio_AiMaterialDesign'),
            content: i18next.t('j-dingtalk-web_pages_ai-studio_AiImageGenerationAndAi'),
            image:
              'https://img.alicdn.com/imgextra/i3/O1CN01a9s5S31GHTXGRhRgq_!!6000000000597-2-tps-192-192.png',
          }}
        />
      )}

      <div className="ai-studio-page">
        {/* PC 端右上角功能区域 */}
        {!isMobile && (
          <PcHeaderActions
            quotaUsage={quotaUsage}
            onOrderClick={handleOrderListClick}
            shareConfig={{
              title: i18next.t('j-dingtalk-web_pages_ai-studio_AiMaterialDesign'),
              content: i18next.t('j-dingtalk-web_pages_ai-studio_AiImageGenerationAndAi'),
              image:
                'https://img.alicdn.com/imgextra/i3/O1CN01a9s5S31GHTXGRhRgq_!!6000000000597-2-tps-192-192.png',
            }}
          />
        )}

      <div className="container">
        {/* 主要内容 */}
        <div className="main-content">
          <img
            className="main-logo"
            src="https://img.alicdn.com/imgextra/i3/O1CN01a9s5S31GHTXGRhRgq_!!6000000000597-2-tps-192-192.png"
            alt="logo"
          />
          <h1 className="page-title">
            {i18next.t('j-dingtalk-web_pages_ai-studio_AiMaterialDesign')}
          </h1>
        </div>

        {/* 功能卡片 */}
        <div className="feature-cards">
          {/* AI 画像生成卡片 */}
          <div className="feature-card" onClick={handleAIImageClick}>
            <div className="card-content">
              <PictureOutlined className="card-icon" />
              <div className="card-title">
                {i18next.t('j-dingtalk-web_pages_ai-studio_AiPortraitGeneration')}
                <RightArrowLOutlined className="arrow-icon" />
              </div>
              <div className="card-description">
                {i18next.t('j-dingtalk-web_pages_ai-studio_TheBackgroundReplacementAndAutomatic')}
              </div>
            </div>
          </div>

          {/* AI 视频生成卡片 */}
          <div className="feature-card" onClick={handleAIVideoClick}>
            <div className="card-content">
              <VideoPlayOutlined className="card-icon" />
              <div className="card-title">
                {i18next.t('j-dingtalk-web_pages_ai-studio_AiVideoGeneration')}
                <RightArrowLOutlined className="arrow-icon" />
              </div>
              <div className="card-description">
                {i18next.t('j-dingtalk-web_pages_ai-studio_MakingHighQualityECommerce')}
              </div>
            </div>
          </div>
        </div>

        {/* 移动端显示 FeaturePromotion，PC 端隐藏 */}
        {isMobile && <FeaturePromotion quotaUsage={quotaUsage} />}
      </div>

        {/* 免费弹窗 */}
        <FreeModal
          visible={freeModalVisible}
          quotaAccessData={quotaAccessData}
          onClose={handleFreeModalClose}
          onReceive={handleFreeModalReceive}
          loading={freeModalLoading}
        />
      </div>
    </>
  );
};

export default AIStudioPage;
