import React, { useState, useEffect } from 'react';
import { i18next } from '@ali/dingtalk-i18n';
import { CopyOutlined, DeleteOutlined, InformationThereLOutlined } from '@ali/ding-icons';
import { Toast } from 'dingtalk-design-mobile';
import ImageUploader from '@/components/ImageUploader';
import { previewImages } from '@/components/imagesPreview';
import { formatCompletionTime } from '@/utils/util';
import { getCreateOrderUrl } from '@/utils/env';
import { isDingTalk, openDualLink } from '@/utils/jsapi';
import { safeJsonParseObject } from '@/utils/parseUtils';
import { copyWithLabel, copyToClipboard } from '@/utils/clipboard';
import $openLink from '@ali/dingtalk-jsapi/api/biz/util/openLink';
import { updateOrder, cancelOrder } from '@/apis/quota';
import { setPageTitle } from '@/utils/jsapi';
import { useOrderCountdown } from '@/hooks/useOrderCountdown';
import { OrderDetailProps, OrderStatus } from '../types';

// Order status configuration
const getOrderStatusConfig = (status: OrderStatus) => {
  const configs = {
    new: {
      title: i18next.t('j-dingtalk-web_pages_order-list_PendingPayment'),
      hightlight: i18next.t(
        'j-dingtalk-web_pages_order-list_components_OrderDetail_TheNumberOfUsageHas'
      ),
      subtitle: i18next.t('j-dingtalk-web_pages_order-list_PendingPaymentSubtitle'),
      emoji: '',
      color: '#FF0E53'
    },
    running: {
      title: i18next.t('j-dingtalk-web_pages_order-list_UnderReview'),
      hightlight: '',
      subtitle: i18next.t('j-dingtalk-web_pages_order-list_UnderReviewSubtitle'),
      emoji: '',
      color: '#FF7621'
    },
    agree: {
      title: i18next.t('j-dingtalk-web_pages_order-list_ReviewPassed'),
      hightlight: '',
      subtitle: i18next.t('j-dingtalk-web_pages_order-list_ReviewPassedSubtitle'),
      emoji: '🎉',
      color: '#0FFFC3'
    },
    disagree: {
      title: i18next.t('j-dingtalk-web_pages_order-list_ReviewFailed'),
      hightlight: '',
      subtitle: i18next.t('j-dingtalk-web_pages_order-list_ReviewFailedSubtitle'),
      emoji: '😭',
      color: 'rgba(255, 255, 255, 0.9)'
    },
    cancel: {
      title: i18next.t('j-dingtalk-web_pages_order-list_OrderCanceled'),
      hightlight: '',
      subtitle: i18next.t('j-dingtalk-web_pages_order-list_OrderCanceledSubtitle'),
      emoji: '',
      color: 'rgba(255, 255, 255, 0.9)'
    },
  };
  return configs[status];
};

const OrderDetail: React.FC<OrderDetailProps> = ({ orderInfo, onBack, isPCMode = false }) => {
  const statusConfig = getOrderStatusConfig(orderInfo.status);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Countdown timer for pending payment orders
  const { timeRemaining } = useOrderCountdown({
    gmtCreate: orderInfo.gmtCreate,
    enabled: orderInfo.status === 'new',
    onCountdownEnd: async () => {
      // Call cancel order API and refresh page on success
      try {
        const response = await cancelOrder({
          orderUuid: orderInfo.orderUuid
        });

        // Only refresh if cancel was successful
        if (response?.success) {
          window.location.reload();
        } else {
          // Show error message if cancellation failed
          Toast.fail({
            content: response?.errorMsg || i18next.t('j-dingtalk-web_pages_order-list_CancelFailed'),
            position: 'top',
            duration: 3
          });
        }
      } catch (error) {
        console.error('Failed to cancel order:', error);
        Toast.fail({
          content: i18next.t('j-dingtalk-web_pages_order-list_NetworkError'),
          position: 'top',
          duration: 3
        });
      }
    }
  });

  useEffect(() => {
    if (!isPCMode) {
      setPageTitle(i18next.t('j-dingtalk-web_pages_order-list_Title'));
    }

    // Parse orderData to get uploaded image
    if (orderInfo.orderData) {
      try {
        const parsedData = safeJsonParseObject(orderInfo.orderData);
        if (parsedData && parsedData.orderScreenshot) {
          setUploadedImage(parsedData.orderScreenshot);
        }
      } catch (error) {
        console.warn('Failed to parse orderData:', error);
      }
    } else {
      setUploadedImage(null);
    }
  }, [orderInfo.orderData, isPCMode]);

  // Handle copy to clipboard
  const handleCopy = (text: string, label: string) => {
    copyWithLabel(text, label);
  };

  // Handle open example reference
  const handleOpenExampleReference = () => {
    // 使用imagesPreview组件展示图片
    previewImages({
      photos: [{
        src: 'https://img.alicdn.com/imgextra/i1/O1CN01cR1aPl1v3BQUoTi8a_!!*************-2-tps-1496-924.png'
      }]
    });
  };

  // Handle copy bank information to clipboard
  const handleCopyBankInfo = () => {
    const bankInfo = [
    `${i18next.t('j-dingtalk-web_pages_order-list_BankName')}：みずほ銀行(銀行コード 0001)`,
    `${i18next.t('j-dingtalk-web_pages_order-list_Branch')}：十八号支店（支店コード 986）`,
    `${i18next.t('j-dingtalk-web_pages_order-list_AccountType')}：当座`,
    `${i18next.t('j-dingtalk-web_pages_order-list_AccountNumber')}：2557000`,
    `${i18next.t('j-dingtalk-web_pages_order-list_AccountName')}：ディントークカブシキガイシャ`].
    join('\n');

    copyToClipboard(bankInfo, i18next.t('j-dingtalk-web_pages_order-list_CopyBankInfoSuccess'));
  };

  const handleSubmitReceipt = async () => {
    // Check if image is uploaded
    if (!uploadedImage) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_order-list_NoImageUploaded'),
        position: 'top',
        duration: 3
      });
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare order data with receipt image
      const orderData = {
        orderScreenshot: uploadedImage
      };

      const response = await updateOrder({
        orderUuid: orderInfo.orderUuid,
        data: JSON.stringify(orderData)
      });

      if (response.success) {
        Toast.success({
          content: i18next.t('j-dingtalk-web_pages_order-list_ReceiptSubmittedSuccess'),
          position: 'top',
          duration: 3
        });
        // 成功之后，刷新当前页面
        window.location.reload();
      } else {
        Toast.fail({
          content:
          response.errorMsg || i18next.t('j-dingtalk-web_pages_order-list_ReceiptSubmitFailed'),
          position: 'top',
          duration: 3
        });
      }
    } catch (error) {
      Toast.fail({
        content: i18next.t('j-dingtalk-web_pages_order-list_NetworkError'),
        position: 'top',
        duration: 3
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle image upload
  const handleImageUpload = (imageUrl: string | null) => {
    setUploadedImage(imageUrl);
  };

  return (
    <div className={`order-detail-page ${isPCMode ? 'pc-mode' : ''}`}>

      {/* Status Header */}
      <div className="status-header" style={{ color: statusConfig.color }}>
        <h1 className="status-title">
          {statusConfig.title} {statusConfig.emoji}
          {orderInfo.status === 'new' && <span className="countdown-timer"> {timeRemaining}</span>}
        </h1>
        <p className="status-subtitle">
          {statusConfig.hightlight && <span>{statusConfig.hightlight}</span>}
          {statusConfig.subtitle}
        </p>
      </div>

      {/* Bank Information Card */}
      <div className="info-card bank-info-card">
        <div className="bank-logo">
          <div className="mizuho-logo">
            <img
              src="https://img.alicdn.com/imgextra/i1/O1CN01xeNSoQ27MDGqcA8jH_!!*************-2-tps-192-192.png"
              alt="mizuho" />
          </div>
          <button
            className="icon-copy"
            onClick={() => handleCopyBankInfo()}
            title={i18next.t('j-dingtalk-web_pages_order-list_CopyBankInfo')}>
            <CopyOutlined />
          </button>
        </div>
        <div className="bank-details">
          <div className="info-row">
            <span className="label">{i18next.t('j-dingtalk-web_pages_order-list_BankName')}：</span>
            <span className="value">
              {i18next.t('j-dingtalk-web_pages_order-list_components_OrderDetail_BankBank')}
            </span>
          </div>
          <div className="info-row">
            <span className="label">{i18next.t('j-dingtalk-web_pages_order-list_Branch')}：</span>
            <span className="value">
              {i18next.t(
                'j-dingtalk-web_pages_order-list_components_OrderDetail_NoBranchStoreBranchStore'
              )}
            </span>
          </div>
          <div className="info-row">
            <span className="label">
              {i18next.t('j-dingtalk-web_pages_order-list_AccountType')}：
            </span>
            <span className="value">
              {i18next.t('j-dingtalk-web_pages_order-list_components_OrderDetail_AsASeat')}
            </span>
          </div>
          <div className="info-row">
            <span className="label">
              {i18next.t('j-dingtalk-web_pages_order-list_AccountNumber')}：
            </span>
            <span className="value">2557000</span>
          </div>
          <div className="info-row">
            <span className="label">
              {i18next.t('j-dingtalk-web_pages_order-list_AccountName')}：
            </span>
            <span className="value">ディントークカブシキガイシャ</span>
          </div>
        </div>
      </div>

      {/* Order Information Card */}
      <div className="info-card order-info-card">
        <div className="info-row">
          <span className="label">
            {i18next.t('j-dingtalk-web_pages_order-list_OrderPrice')}：
          </span>
          <span className="value">2200円</span>
        </div>
        <div className="info-row order-number-row">
          <div>
            <span className="label">
              {i18next.t('j-dingtalk-web_pages_order-list_OrderNumber')}：
            </span>
            <span className="value">{orderInfo.orderUuid}</span>
          </div>
          <button
            className="icon-copy"
            onClick={() =>
            handleCopy(
              orderInfo.orderUuid,
              i18next.t('j-dingtalk-web_pages_order-list_OrderNumber')
            )
            }>
            <CopyOutlined />
          </button>
        </div>
        <div className="info-row">
          <span className="label">
            {i18next.t('j-dingtalk-web_pages_order-list_OrderTime')}：
          </span>
          <span className="value">{formatCompletionTime(new Date(orderInfo.gmtCreate))}</span>
        </div>
      </div>

      {/* Transfer Receipt Section (only for pending_payment status) */}
      <div className="transfer-receipt-section">
        <div className="section-left">
          <div className="section-title">
            <span className="title-text">
              {i18next.t('j-dingtalk-web_pages_order-list_TransferReceipt')}
            </span>
            <button className="example-btn" onClick={() => handleOpenExampleReference()}>
              <InformationThereLOutlined />
              {i18next.t('j-dingtalk-web_pages_order-list_ExampleReference')}
            </button>
          </div>
          <p className="receipt-tip">{i18next.t('j-dingtalk-web_pages_order-list_ReceiptTip')}</p>
        </div>
        <div className="upload-area">
          <ImageUploader
            uploadText={i18next.t('j-dingtalk-web_pages_order-list_UploadPhoto')}
            value={uploadedImage}
            showReuploadBtn={false}
            onChange={handleImageUpload}
            disabled={orderInfo.status === 'disagree' || orderInfo.status === 'cancel'}
          />
          {orderInfo.status === 'new' && uploadedImage &&
          <div className="delete-image-overlay">
            <div
              className="delete-btn"
              onClick={() => handleImageUpload(null)}
            >
              <DeleteOutlined />{i18next.t('j-dingtalk-web_pages_order-list_components_OrderDetail_Delete')}
            </div>
          </div>
          }
        </div>
      </div>

      {/* Submit Button - Only show when no image is uploaded */}
      {orderInfo.status === 'new' &&
      <button className="submit-btn" onClick={handleSubmitReceipt} disabled={isSubmitting}>
          {isSubmitting ?
        i18next.t('j-dingtalk-web_pages_order-list_Submitting') :
        i18next.t('j-dingtalk-web_pages_order-list_SubmitInfo')}
        </button>
      }
      {(orderInfo.status === 'disagree' || orderInfo.status === 'cancel') &&
      <button
        className="submit-btn"
        onClick={() => {
          const url = getCreateOrderUrl();
            if (isDingTalk()) {
              $openLink({
                url: openDualLink(url),
              });
            } else {
              window.open(url);
            }
          }}
      >
        {i18next.t('j-dingtalk-web_pages_order-list_RePurchase')}
      </button>
      }
    </div>);
};

export default OrderDetail;
