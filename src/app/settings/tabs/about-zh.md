# ⚡️ [关于 AgentFlow](https://github.com/bytedance/deer-flow)

> **源于开源，回馈开源**

**AgentFlow**（**深度探索**和**高效研究**流程）是一个由社区驱动的 AI 自动化框架，受到开源社区卓越贡献的启发。我们的使命是将语言模型与专业工具无缝集成，用于网络搜索、爬取和 Python 代码执行等任务——同时回馈使这种创新成为可能的社区。

---

## 🌟 GitHub 仓库

在 GitHub 上探索 DeerFlow：[github.com/bytedance/deer-flow](https://github.com/bytedance/deer-flow)

---

## 📜 软件许可证

AgentFlow 作为开源项目，在 **MIT 许可证** 下分发。

---

## 🙌 致谢

我们衷心感谢使 AgentFlow 成为现实的开源项目和贡献者。我们真正站在巨人的肩膀上。

### 核心框架
- **[LangChain](https://github.com/langchain-ai/langchain)**：支持我们 LLM 交互和链的卓越框架。
- **[LangGraph](https://github.com/langchain-ai/langgraph)**：实现复杂的多智能体编排。
- **[Next.js](https://nextjs.org/)**：构建 Web 应用程序的前沿框架。

### UI 库
- **[Shadcn](https://ui.shadcn.com/)**：支持我们 UI 的简约组件。
- **[Zustand](https://zustand.docs.pmnd.rs/)**：令人惊叹的状态管理库。
- **[Framer Motion](https://www.framer.com/motion/)**：出色的动画库。
- **[React Markdown](https://www.npmjs.com/package/react-markdown)**：具有可定制性的卓越 markdown 渲染。
- **[SToneX](https://github.com/stonexer)**：感谢他对逐字符视觉效果的宝贵贡献。

这些杰出的项目构成了 AgentFlow 的骨干，体现了开源协作的变革力量。

### 特别感谢
最后，我们要向 `AgentFlow` 的核心作者表达衷心的感谢：

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

没有他们的愿景、热情和奉献，`AgentFlow` 就不会有今天的成就。
