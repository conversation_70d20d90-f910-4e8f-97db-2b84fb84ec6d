// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import { env } from "~/env";

/**
 * Get API base URL based on current domain
 * For static hosting, we determine the API URL at runtime based on the current domain
 */
function getApiBaseUrl(): string {
  // If API URL is explicitly set via environment variable, use it (for development/testing)
  if (env.NEXT_PUBLIC_API_URL) {
    return env.NEXT_PUBLIC_API_URL;
  }

  // Default fallback (for SSR/build time or unknown domains)
  return `https://${window.location.host}/api/`;
}

export function resolveServiceURL(path: string) {
  let BASE_URL = getApiBaseUrl();
  if (!BASE_URL.endsWith("/")) {
    BASE_URL += "/";
  }
  return new URL(path, BASE_URL).toString();
}
