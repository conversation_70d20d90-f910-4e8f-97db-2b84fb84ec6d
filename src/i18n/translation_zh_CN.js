export default {
  "j-agent-web_pages_category_components_CategorySelect_NoCategoryDataIsAvailable": "没有可用的类目数据",
  "j-agent-web_pages_category_LevelCategory": "一级类目",
  "j-agent-web_pages_category_ApplyFilterCriteria": "应用筛选条件:",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory": "一级类目",
  "j-agent-web_pages_category_components_CategorySelect_SecondaryCategory": "二级类目",
  "j-agent-web_pages_category_components_CategorySelect_LevelCategory_1": "三级类目",
  "j-agent-web_pages_category_ClosedSuccessfully": "关闭成功",
  "j-agent-web_pages_category_FailedToClose": "关闭失败",
  "j-agent-web_pages_comments_CurrentlyScoringIsNotSupported": "暂不支持评分",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CommentsDatacommdesc": "评论（{{dataCommDesc}}）",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentFeatures": "客户评论特征：",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_OpportunityList": "热销榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SoaringList": "飙升榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_NewProductList": "潜力榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_SalesList": "销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_DailySalesList": "日销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_WeeklySalesList": "周销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductGallery_MonthlySalesList": "月销量榜",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_Comments": "评论",
  "j-agent-web_utils_util_AYearAgo": "一年前",
  "j-agent-web_utils_util_YearsYearsAgo": "{{years}}年前",
  "j-agent-web_utils_util_AMonthAgo": "一个月前",
  "j-agent-web_utils_util_MonthsMonthsAgo": "{{months}}个月前",
  "j-agent-web_utils_util_ADayAgo": "一天前",
  "j-agent-web_utils_util_DaysDaysAgo": "{{days}}天前",
  "j-agent-web_utils_util_AnHourAgo": "一个小时前",
  "j-agent-web_utils_util_HoursHoursAgo": "{{hours}}小时前",
  "j-agent-web_utils_util_OneMinuteAgo": "一分钟前",
  "j-agent-web_utils_util_MinutesMinutesAgo": "{{minutes}}分钟前",
  "j-agent-web_utils_util_JustNow": "刚刚",
  "j-agent-web_utils_util_SecondsSecondsAgo": "{{seconds}}秒前",
  "j-agent-web_pages_commodityDetail_components_ProductPage_SorryThereIsNoProduct": "抱歉，暂无商品数据",
  "j-agent-web_pages_category_SubscriptionSuccessful": "订阅成功",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadFailed": "{{fileName}} 上传失败",
  "j-agent-web_pages_agentForm_components_FileUploader_FilenameUploadedSuccessfully": "{{fileName}} 上传成功",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFailed": "上传失败:",
  "j-agent-web_pages_agentForm_components_FileUploader_UploadFiles": "上传文件",
  "j-agent-web_pages_agentForm_components_FileUploader_SupportedFormatPhtASingle": "支持格式：.pht，单个文件不超过200MB，最多上传",
  "j-agent-web_pages_agentForm_components_FileUploader_Files": "个文件",
  "j-agent-web_pages_agentForm_components_FileUploader_PreviewImage": "预览图片",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelId": "模型id",
  "j-agent-web_pages_agentForm_components_FormComponent_Copy": "复制",
  "j-agent-web_pages_agentForm_components_FormComponent_CopiedSuccessfully": "复制成功",
  "j-agent-web_pages_agentForm_components_FormComponent_UpdatedSuccessfully": "更新成功",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUploadFailedErr": "模型上传失败{{err}}",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionError": "表单提交错误:",
  "j-agent-web_pages_agentForm_components_FormComponent_FormSubmissionFailedPleaseTry": "表单提交失败，请重试！",
  "j-agent-web_pages_agentForm_components_FormComponent_TheFormHasBeenReset": "表单已重置",
  "j-agent-web_pages_agentForm_components_FormComponent_SelectAModelToUpdate": "请选择要更新的模型",
  "j-agent-web_pages_agentForm_components_FormComponent_TheModelToBeUpdated": "选择了需要更新的模型",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelName": "模型名称",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterAModelName": "请输入模型名称",
  "j-agent-web_pages_agentForm_components_FormComponent_UploadModelFiles": "模型文件上传",
  "j-agent-web_pages_agentForm_components_FormComponent_PromptEditingSupportsMarkdown": "Prompt编辑支持Markdown",
  "j-agent-web_pages_agentForm_components_FormComponent_EnterPrompt": "请输入prompt",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpdate": "模型更新",
  "j-agent-web_pages_agentForm_components_FormComponent_ModelUpload": "模型上传",
  "j-agent-web_pages_agentForm_components_FormComponent_Reset": "重置",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Bold": "粗体",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Italic": "斜体",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_OrderedList": "有序列表",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_UnorderedList": "无序列表",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Link": "链接",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Edit": "编辑",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_MarkdownFormatEditingIsSupported": "支持 Markdown 格式编辑，可以输入文本、列表、链接等...",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_Preview": "预览",
  "j-agent-web_pages_agentForm_components_MarkdownEditor_NoContentPreview": "无内容预览",
  "j-agent-web_pages_agentForm_AddModel": "新增模型",
  "j-agent-web_pages_agentForm_UpdateModelPrompt": "更新模型prompt",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainOssConfiguration": "获取OSS配置失败:",
  "j-agent-web_pages_agentForm_services_ossService_FailedToObtainUploadCredentials": "获取上传凭证失败",
  "j-agent-web_pages_agentForm_services_ossService_FailedToUploadTheFile": "上传文件失败:",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMost": "该平台昨日全品类（或订阅某些品类）当日销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostDaily": "该平台昨日全品类（或订阅某些品类）当日销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostWeekly": "该平台本周全品类（或订阅某些品类）当周销量最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformSoldTheMostMonthly": "该平台本月全品类（或订阅某些品类）当月销量最多的商品；",
  "j-agent-web_pages_cardTips_NewProductsWithTheLargest": "此平台所有类别（或订阅类别）在过去30天内推出后喜欢人数最多的新产品；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldThe": "该平台昨日全品类（或订阅某些品类）当日销量增速相较前日最多的商品；",
  "j-agent-web_pages_cardTips_ThePlatformYesterdaySoldAll": "此平台所有类别（或订阅类别）在过去30天上榜天数占比最长的商品。",
  "j-agent-web_pages_cardTips_DataDescription": "数据说明",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_StoreNameProductstorename": "店铺：",
  "j-agent-web_pages_productCuration_components_ProductList_Rating": "评分：",
  "j-agent-web_pages_productCuration_ProductSelectionCenter": "商品选品中心",
  "j-agent-web_pages_productCuration_UnableToObtainBizid": "获取不到bizId",
  "j-agent-web_pages_productCuration_FailedToObtainProductList": "获取商品列表失败",
  "j-agent-web_pages_productCuration_FailedToSubmitTheProduct": "提交商品失败",
  "j-agent-web_pages_productCuration_LoadingGoods": "加载商品中...",
  "j-agent-web_pages_productCuration_ProductslengthItemsInTotalSelectedcount": "共 {{productsLength}} 个商品，已选择 {{selectedCount}} 个商品",
  "j-agent-web_pages_productCuration_DeselectAll": "取消全选",
  "j-agent-web_pages_productCuration_SelectAllCurrentPage": "全选当前页",
  "j-agent-web_pages_productCuration_NoProductDataAvailable": "暂无商品数据",
  "j-agent-web_pages_productCuration_SubmitSelectionSelectedcount": "提交选品({{selectedCount}})",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductSourcing": "1688相似商品",
  "j-agent-web_pages_productOrigin_components_ProductPage_FailedToObtainData": "获取数据失败",
  "j-agent-web_pages_productOrigin_components_ProductPage_ZozoGrid": "ZOZOTOWN价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AmazonGrid": "Amazon价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RednoteGrid": "参考价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TiktokGrid": "抖音价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_TaobaoGrid": "淘宝价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_AlibabaGrid": "1688Japan价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_RakutenGrid": "乐天价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_ManualGrid": "价格",
  "j-agent-web_pages_productOrigin_components_ProductPage_Details": "详情",
  "j-agent-web_pages_productOrigin_components_ProductPage_Favorites": "收藏数",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductdatafavnumPeople": "{{productDataFavNum}} 人",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoCollection": "暂无收藏",
  "j-agent-web_pages_productOrigin_components_ProductPage_List": "榜单",
  "j-agent-web_pages_productOrigin_components_ProductPage_ProductRating": "商品评分",
  "j-agent-web_pages_productOrigin_components_ProductPage_NoScore": "暂无评分",
  "j-agent-web_pages_productOrigin_components_ProductPage_SimilarProducts": "1688相似商品",
  "j-agent-web_pages_category_components_CategorySelect_AllCategories": "全部类目",
  "j-agent-web_pages_commodityDetail_components_ProductInfo_FindSimilarity": "找相似",
  "j-agent-web_pages_productOriginV2_components_ProductPage_TodaySSalesVolume": "今日销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfLikes": "喜欢数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_DailyGrowth": "日增长",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NumberOfDaysOnThe": "上榜天数",
  "j-agent-web_pages_productOriginV2_components_ProductPage_SalesVolume": "销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_MonthlySales": "近90天销量",
  "j-agent-web_pages_productOriginV2_components_ProductPage_RepurchaseRate": "回购率",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ProductEvaluation": "商品评价",
  "j-agent-web_pages_productOriginV2_components_ProductPage_ListSource": "榜单来源",
  "j-agent-web_pages_productOriginV2_components_ProductPage_NoListInformation": "暂无榜单信息",
  "j-agent-web_pages_productOriginV2_components_ProductPage_FactoryPrice": "工厂直销价格",
  "j-agent-web_pages_productCuration_components_ProductList_ViewProductDetails": "查看商品详情",
  "j-agent-web_pages_productOrigin_components_ProductPage_ShareContent": "寻找1688japan的你，来这里！海量中国商品直供日本，价格更低、选择更多！",
  "j-agent-web_pages_productCuration_SuccessfullySubmitted": "已成功提交 {{successItemListLen}} 个商品到Feeds流，失败 {{failItemListLen}} 个商品",
  "j-dingtalk-web_components_ImageSwiper_ProductPortrait": "商品图片",
  "j-dingtalk-web_components_OptimizedImage_PortraitFailure": "图像加载失败",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_ForProductInformationSee": "未找到商品",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pieces": "件",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ProductPortrait": "商品图片",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NoMore": "没有更多了",
  "j-dingtalk-web_pages_category_FailedToObtainCategoryData": "获取类目数据失败",
  "j-dingtalk-web_pages_category_SelectASecondaryCategory": "请选择二级类目",
  "j-dingtalk-web_pages_commodityDetail_components_ProductPage_MoreProducts": "更多商品",
  "j-dingtalk-web_pages_category_CategorySelectionSucceeded": "订阅成功",
  "j-dingtalk-web_pages_category_CategorySelectionFailed": "订阅失败，请重试",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_MoreDetails": "查看详情",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CommentDetails": "评论详情",
  "j-agent-web_pages_commodityDetail_components_CustomerReviews_CustomerCommentCount": "{{commentCount}}条全球评分",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_StarRating": "{{rating}}颗星",
  "j-dingtalk-web_pages_commodityDetail_components_AmazonComments_ScoreFullScore": "{{score}}分（满分5分）",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_CustomerFeedback": "客户反馈",
  "j-dingtalk-web_pages_commodityDetail_components_ProductInfo_GeneratedByAiBasedOn": "基于客户提交内容，由AI生成",
  "j-agent-web_pages_productOrigin_components_ProductPage_Price": "价格",
  "j-dingtalk-web_pages_premiumStore_StoreDetails": "店铺详情",
  "j-dingtalk-web_pages_premiumStore_ComprehensiveEvaluation": "综合评价：",
  "j-dingtalk-web_pages_premiumStore_ProductQuality": "商品质量：",
  "j-dingtalk-web_pages_premiumStore_DeliverySpeed": "发货速度：",
  "j-dingtalk-web_pages_premiumStore_ConsultingServices": "咨询服务：",
  "j-dingtalk-web_pages_premiumStore_SimilarProducts": "类似商品",
  "j-dingtalk-web_pages_topMerchant_GoodNumberDetails": "博主详情",
  "j-dingtalk-web_pages_topMerchant_FailedToObtainData": "获取数据失败",
  "j-dingtalk-web_pages_topMerchant_Fans": "粉丝",
  "j-dingtalk-web_pages_topMerchant_NumberOfInteractions": "互动次数",
  "j-dingtalk-web_pages_topMerchant_DataOverview": "数据概览",
  "j-dingtalk-web_pages_topMerchant_NumberOfLikesReceived": "获赞数",
  "j-dingtalk-web_pages_topMerchant_NumberOfNotes": "笔记数",
  "j-dingtalk-web_pages_topMerchant_NumberOfCooperativeBrands": "合作品牌数",
  "j-dingtalk-web_pages_topMerchant_Mockmerchantinfobrandcount": "{{merchantInfoGoodsNum}}个",
  "j-dingtalk-web_pages_topMerchant_NumberOfConcerns": "关注数",
  "j-dingtalk-web_pages_topMerchant_StyleOverview": "风格概览",
  "j-dingtalk-web_pages_topMerchant_HighestInteraction": "互动最高",
  "j-dingtalk-web_pages_topMerchant_RecentlyReleased": "最近发布",
  "j-dingtalk-web_pages_topMerchant_TheGoodsMentionedInThis": "该笔记中提到的商品",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts": "类似商品",
  "j-dingtalk-web_pages_topMerchant_ProductsSoldByTa": "TA售卖的商品",
  "j-dingtalk-web_pages_topMerchant_HighestSalesVolume": "销量最高",
  "j-dingtalk-web_pages_topMerchant_SimilarProducts_1": "类似商品",
  "j-dingtalk-web_components_ImageSwiper_LookingForSimilarProducts": "寻找类似产品",
  "j-agent-web_pages_productOrigin_components_ProductPage_CopiedToClipboard": "productId 已复制",
  "j-dingtalk-web_pages_category_CategorySelect_DailyNewspaper": "日报",
  "j-dingtalk-web_pages_category_CategorySelect_WeeklyNewspaper": "周报",
  "j-dingtalk-web_pages_category_CategorySelect_MonthlyReport": "月报",
  "j-dingtalk-web_pages_category_CategorySelect_EveryWorkingDay": "每个工作日",
  "j-dingtalk-web_pages_category_CategorySelect_EveryDay": "每天",
  "j-dingtalk-web_pages_category_CategorySelect_EveryFriday": "每周五",
  "j-dingtalk-web_pages_category_CategorySelect_ThOfEachMonth": "每月1日",
  "j-dingtalk-web_pages_category_CategorySelect_SubscriptionSettings": "订阅设置",
  "j-dingtalk-web_pages_category_CategorySelect_SelectDate": "选择日期",
  "j-dingtalk-web_pages_category_CategorySelect_SelectTime": "选择时间",
  "j-dingtalk-web_pages_category_CategorySelect_Cancel": "取消",
  "j-dingtalk-web_pages_category_CategorySelect_Confirm": "确认",
  "j-dingtalk-web_pages_category_Comprehensive": "综合",
  "j-dingtalk-web_pages_category_PleaseSelectALevelCategory": "请选择一级类目",
  "j-dingtalk-web_pages_category_SelectALevelCategory": "请选择三级类目",
  "j-dingtalk-web_components_Loading_Loading": "加载中",
  "j-dingtalk-web_pages_category_CategorySelect_Loading": "加载中",
  "j-dingtalk-web_pages_category_CategorySwitchingFailedPleaseTry": "类目切换失败，请重试",
  "j-dingtalk-web_pages_premiumStore_MonthlySalesProductmonthsolddisplayPieces": "月销售量{{productMonthSoldDisplay}}件",
  "j-dingtalk-web_pages_premiumStore_FailedToObtainStoreData": "店铺数据加载失败~",
  "j-dingtalk-web_components_SwipeableProductCard_NotInterested": "不感兴趣",
  "j-dingtalk-web_components_SwipeableProductCard_Interested": "感兴趣",
  "j-dingtalk-web_components_SwipeableProductCard_Approve": "通过",
  "j-dingtalk-web_components_SwipeableProductCard_Reject": "不通过",
  "j-dingtalk-web_components_SwipeableProductList_NoProductDataAvailable": "暂无商品数据",
  "j-dingtalk-web_components_SwipeableProductList_SlideLeftAndRightTo": "← 左右滑动切换商品 →",
  "j-dingtalk-web_components_SwipeableProductList_IMNotInterestedIn": "↑ 上滑不感兴趣 ↓ 下滑感兴趣",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFakeAndInferiorProducts": "举报假冒伪劣",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportThatThisProductIs": "举报此商品是假冒伪劣商品",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotYet": "暂不",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ConfirmReport": "确认举报",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_TheParameterIsIncorrectAnd": "参数错误，无法举报",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportSuccessful": "举报成功",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_ReportFailedPleaseTryAgain": "举报失败，请稍后重试",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_UnknownError": "未知错误",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditSuccess": "审核{{status}}",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_AuditFailed": "审核失败，请稍后重试",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsInterested": "已标记为感兴趣",
  "j-dingtalk-web_pages_sourcing_components_Page_MarkedAsNotInterested": "已标记为不感兴趣",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditSuccess": "审核{{status}}成功",
  "j-dingtalk-web_pages_sourcing_components_Page_AuditFailed": "审核失败，请稍后重试",
  "j-dingtalk-web_pages_sourcing_components_Page_SimilarSources": "1688相似货源",
  "j-dingtalk-web_pages_topMerchant_Favorites": "收藏数",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Pass": "通过",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_NotPassed": "不通过",
  "j-dingtalk-web_utils_util_NumberUnitTenThousand": "万",
  "j-dingtalk-web_utils_util_NumberUnitHundredMillion": "亿",
  "j-dingtalk-web_pages_topMerchant_LoadMore": "加载更多",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully": "图片上传成功",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed": "图片上传失败",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailedPleaseTry": "图片上传失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage": "点击上传图片",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading": "上传中...",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageSizeError": "图片尺寸不符合要求，要求：最小{{minWidth}}x{{minHeight}}，最大{{maxWidth}}x{{maxHeight}}。",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError": "图片加载失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageSavedToLocal": "图片已保存到本地",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageTooSmall": "图片尺寸过小，当前尺寸：{{currentWidth}}x{{currentHeight}}，要求最小：{{minWidth}}x{{minHeight}}",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_OnlyImageFilesAllowed": "仅支持图片文件",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_FileTooLarge": "文件太大，请选择小于10MB的图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ImageSpecification": "图片规范",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MaterialType": "素材类型：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheElementsAreSimpleThe": "1、元素简洁，光线充足、商品清晰、轮廓明显呈现",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ItIsRecommendedToUpload": "2、建议上传有模特图、或真人展示商品图（半身、近景效果更佳）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Size": "尺寸规格：",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_SupportsJpgPngFormatWith": "1、支持 JPG/PNG格式，大小不超过 5M",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheShortSideIsNot": "2、短边不小于 300PX，长宽比在5:2 和 2:5之间",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CorrectExample": "正确示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HandheldProductWithClearText": "手持商品，文字清晰",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheProductIsClearAnd": "商品清晰，轮廓明显",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelHalfLengthDisplayClothing": "模特半身展示服装",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelTrialProducts": "模特试用产品",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ErrorExample": "错误示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StitchingPicture": "拼接图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_UnclearSubject": "主体不清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CreativeDescriptionReference": "创意描述参考",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_DescribeThePictureAndAction": "结合图片描述你想生成的画面和动作；建议使用“主体+动作”的描述方式，如“模特微笑向前走”",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ReferenceExample": "参考示例",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_MenSlowlyPickUpThe": "男人缓缓拿起酒杯",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptMenSlowlyPickUp": "Prompt：男人缓缓拿起酒杯",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_TheLensSlowlyRotatesTo": "镜头缓缓向右侧旋转，展示游戏主机机箱",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptTheLensSlowlyRotates": "Prompt：镜头缓缓向右侧旋转，展示游戏主机机箱",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ModelPosingShowingYogaClothes": "模特摆造型，展示瑜伽服",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PromptModelPosingShowingYoga": "Prompt：模特摆造型，展示瑜伽服",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_ContentYouDonTWant": "你不希望出现的内容（非必填）",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopiedToCreativeDescription": "已复制到创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_CopyToCreativeDescription": "复制到创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseUploadAnImage": "请上传图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_PleaseEnterAForwardDescription": "请输入正向描述",
  "j-dingtalk-web_pages_aiVideo_InsufficientQuotaPleaseRecharge": "可使用次数不足，请充值后继续使用",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Hd": "高清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_StandardClear": "标清",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_FirstFrameVideoPicture": "首帧视频图片",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_YourCreativeDescription": "你的创意描述",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsModelDisplay": "例如：模特展示产品",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_InputSuchAsDistortionDistortion": "输入比如失真、扭曲、变形、低质量等",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDefinition": "视频清晰度",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_VideoDuration": "视频时长",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_GenerateNow": "立即生成",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_HelpInformation": "帮助信息",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Generating": "生成中...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ItIsExpectedToBe": "预计在1～3分钟完成，再此期间您可以退出",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Hd": "高清",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_StandardClear": "标清",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_PleaseClickTheFullScreen": "请点击视频右下角全屏按钮",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThePraiseIsCanceled": "取消点赞成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ThumbsUpSuccessfully": "点赞成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheOperationFailedPleaseTry": "操作失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_TheCancellationPointIsStepped": "取消点踩成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ClickSuccessfully": "点踩成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifAlreadyExistsStartDownloading": "GIF已存在，开始下载...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationCompleted": "GIF生成完成！",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedToStart": "GIF生成启动失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationHasStartedPlease": "GIF生成已开始，请稍候...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailedPleaseTry": "GIF生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationFailed": "GIF生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_GifGenerationTimeout": "GIF生成超时",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_UnknownStatusResultstatus": "未知状态: {{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegeneratingVideo": "正在重新生成视频...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationHasStartedPleaseWait": "重新生成已开始，请稍候...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailed": "重新生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_RegenerationFailedPleaseTryAgain": "重新生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConfirmDeletion": "确认删除",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_AreYouSureYouWant": "您确定要删除此视频吗？",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Cancel": "取消",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_Delete": "删除",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DeletedSuccessfully": "删除成功",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToDeletePleaseTry": "删除失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_WaitingForGeneration": "等待生成...",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_DownloadGif": "下载GIF",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_NoVideoAvailable": "暂无视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_StartCreatingYourFirstAi": "开始创建您的第一个AI视频吧！",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateAVideo": "+ 创建视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToLoad": "加载失败",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_FailedToObtainTheVideo": "获取视频列表失败，请重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_Retry": "重试",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_CreateANewVideo": "创建新视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingVideoList": "正在加载视频列表...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_LoadingMore": "正在加载更多...",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_AllVideosAreDisplayed": "已显示全部视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_SlideDownToLoadMore": "向下滑动加载更多",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationCompleted": "视频生成完成！",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailed": "视频生成失败",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationFailedPleaseTry": "视频生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationTimedOutPlease": "视频生成超时，请重试",
  "j-dingtalk-web_pages_aiVideo_VideoGenerationTakingLongerThan": "视频生成时间比预期更长，请耐心等待...",
  "j-dingtalk-web_pages_aiVideo_UnknownStatusResultstatus": "未知状态: {{resultStatus}}",
  "j-dingtalk-web_pages_aiVideo_AnUnknownErrorOccurredWhile": "视频生成遇到未知错误，请重试",
  "j-dingtalk-web_pages_aiVideo_QueryStatusFailed": "查询状态失败",
  "j-dingtalk-web_pages_aiVideo_FailedToQueryTheVideo": "查询视频生成状态失败，请重试",
  "j-dingtalk-web_pages_aiVideo_FailedToObtainTheVideo": "获取视频列表失败",
  "j-dingtalk-web_pages_aiVideo_PleaseUploadAnImage": "请上传图片",
  "j-dingtalk-web_pages_aiVideo_PleaseEnterAForwardDescription": "请输入正向描述",
  "j-dingtalk-web_pages_aiVideo_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_aiVideo_FailedToGeneratePleaseTry": "生成失败，请重试",
  "j-dingtalk-web_pages_aiVideo_Loading": "正在加载...",
  "j-dingtalk-web_pages_premiumStore_FailedToLoadStoreData": "店铺数据加载失败~",
  "j-dingtalk-web_utils_download_File": "文件",
  "j-dingtalk-web_utils_download_Video": "视频",
  "j-dingtalk-web_utils_download_DownloadingFiletype": "正在下载{{fileType}}...",
  "j-dingtalk-web_utils_download_FiletypeDownloadedSuccessfully": "{{fileType}}下载成功",
  "j-dingtalk-web_utils_download_FiletypeFailedToDownloadPlease": "{{fileType}}下载失败，请重试",
  "j-dingtalk-web_utils_download_DownloadIsNotSupportedIn": "当前环境不支持下载功能",
  "j-dingtalk-web_utils_download_StartDownloadingFiletype": "开始下载{{fileType}}",
  "j-dingtalk-web_utils_download_FailedToDownloadFiletypePlease": "下载{{fileType}}失败，请重试",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInThe": "{{fileType}}已在浏览器中打开，请手动保存",
  "j-dingtalk-web_utils_download_FiletypeIsOpenInA": "{{fileType}}已在新窗口中打开，请手动保存",
  "j-dingtalk-web_utils_download_SaveAs": "另存为",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_ConvertingToGif": "转换为GIF",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FullscreenNotAllowedInIframe": "当前环境不支持全屏功能，请在新窗口中打开视频",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FullscreenNotSupported": "您的浏览器不支持全屏功能",
  "j-dingtalk-web_pages_aiVideo_components_VideoItem_FullscreenUnavailable": "全屏功能暂时不可用",
  "j-dingtalk-web_pages_premiumStore_NumberOfFans": "粉丝数",
  "j-dingtalk-web_pages_premiumStore_PositiveRate": "好评率",
  "j-dingtalk-web_pages_topMerchant_NumberOfProducts": "商品数",
  "j-dingtalk-web_pages_topMerchant_ProductsMentionedInThisVideo": "该视频中提到的商品",
  "j-dingtalk-web_components_InfiniteList_NoDataAvailable": "暂无数据",
  "j-dingtalk-web_components_InfiniteList_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Today": "今天",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_Yesterday": "昨天",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_DiffdaysDaysAgo": "{{diffDays}}天前",
  "j-dingtalk-web_pages_my-comment_components_CommentCard_MMonthDDay": "M月D日",
  "j-dingtalk-web_pages_my-comment_MyComments": "我的评论",
  "j-dingtalk-web_pages_my-comment_NoCommentRecords": "没有评论记录",
  "j-dingtalk-web_pages_my-comment_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NoItemsForCollection": "暂无收藏的商品",
  "j-dingtalk-web_pages_my-favorites_components_ProductView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_Contribution": "投稿",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NoStoreForCollection": "暂无收藏的店铺",
  "j-dingtalk-web_pages_my-favorites_components_StoreView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NoFavoriteProducts": "没有收藏的产品",
  "j-dingtalk-web_pages_my-favorites_components_TrendingView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-favorites_MyCollection": "我的收藏",
  "j-dingtalk-web_pages_my-favorites_Video": "视频",
  "j-dingtalk-web_pages_my-favorites_Shop": "店铺",
  "j-dingtalk-web_pages_my-favorites_Commodity": "商品",
  "j-dingtalk-web_pages_my-history_components_ProductView_NoBrowsingHistory": "暂无浏览历史",
  "j-dingtalk-web_pages_my-history_components_ProductView_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_my-history_BrowsingHistory": "浏览历史",
  "j-dingtalk-web_pages_my-history_Video": "视频",
  "j-dingtalk-web_pages_my-history_Commodity": "商品",
  "j-dingtalk-web_pages_my-like_components_ProductCard_FailedToParseProductPayload": "解析 product payload 失败:",
  "j-dingtalk-web_pages_my-like_ILikeIt": "我赞过的",
  "j-dingtalk-web_pages_my-like_IDonTLikeThe": "没有点赞的产品",
  "j-dingtalk-web_pages_my-like_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_work-center_Workbench": "工作台",
  "j-dingtalk-web_pages_work-center_ECommerceApplications": "电商应用",
  "j-dingtalk-web_pages_work-center_ECommerceRelatedApplicationTools": "电商相关的应用工具",
  "j-dingtalk-web_pages_work-center_CommoditySalesStatistics": "商品销售统计表",
  "j-dingtalk-web_pages_work-center_InventoryManagement": "库存管理",
  "j-dingtalk-web_pages_work-center_UserEvaluationAnalysisTable": "用户评价分析表",
  "j-dingtalk-web_pages_work-center_SupplyChainManagement": "供应链管理",
  "j-dingtalk-web_pages_work-center_GeneralApplication": "通用应用",
  "j-dingtalk-web_pages_work-center_GeneralOfficeApplications": "通用办公应用",
  "j-dingtalk-web_pages_work-center_AiTable": "AI表格",
  "j-dingtalk-web_pages_work-center_Document": "文档",
  "j-dingtalk-web_pages_work-center_Schedule": "日程",
  "j-dingtalk-web_pages_work-center_Meeting": "会议",
  "j-dingtalk-web_pages_work-center_FailedToOpenTheLink": "打开链接失败:",
  "j-dingtalk-web_pages_topMerchant_Sold": "已售",
  "j-dingtalk-web_pages_topMerchant_Pieces": "件",
  "j-dingtalk-web_pages_work-center_AiMaterial": "AI 素材",
  "j-dingtalk-web_pages_work-center_EvaluationAndAnalysis": "评价分析",
  "j-dingtalk-web_pages_work-center_EmailAddress": "邮箱",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_DownloadSuccess": "下载成功",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_DownloadFailed": "下载失败，请重试",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_FeatureComingSoon": "功能即将上线",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_Download": "下载",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_AIVideoGeneration": "AI视频生成",
  "j-dingtalk-web_pages_ai-image_components_ImagePreview_HDUpscale": "高清放大",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_ScienceFictionLight": "科幻之光",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BlackGlassBoothSphereAnd": "黑色玻璃展台，球体和台阶",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_DarkTableLampStrip": "暗台灯条",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_MetalCountertopVerticalLedLight": "金属台面，垂直 LED 灯带发出明亮",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_NeonFire": "霓虹灯火",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_AsphaltRoadReflectionBlurredColor": "雨后沥青路、倒影、迷离、颜色、城市灯光",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BrightWoodenTable": "明亮木台",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WoodenDeskSquareAndSteps": "木质书桌，方形和台阶，亮色调",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BlueBooth": "蓝色展台",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_BoothGoldBlueAdvanced": "展台、金色、蓝色、高级",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_GoldInlaidRock": "镶金岩石",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_MetalCountertopDarkTextureRock": "金属台面深色纹理岩石，以黑色为主略带金色点缀，垂直 LED 灯带发出明亮",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WhiteSpace": "白色空间",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_WhiteSpaceBackgroundLightingEffect": "白色空间、背景光效、明亮环境",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_FillIn": "填入",
  "j-dingtalk-web_pages_ai-image_components_BackgroundSelector_Cancel": "取消",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ProcessingMatting": "正在处理抠图...",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageMattingCompleted": "图片抠图完成",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageProcessingExceptionImageProcessing": "图片处理异常 (Image processing error):",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageProcessingFailedUseThe": "图片处理失败，使用原图",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_URLContainsChinese": "图片URL不能包含中文字符",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_UnsupportedImageFormat": "不支持的图片格式，仅支持JPEG、JPG、PNG、BMP、WEBP格式",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageTooSmall": "图片分辨率过小，需要大于32×32像素",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageTooLarge": "图片分辨率过大，需要小于2000×2000像素",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_MaxEdgeExceeded": "图片最长边超过限制，不能超过1999像素",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_FileSizeExceeded": "图片文件过大，不能超过3MB",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageLoadError": "图片加载失败，请检查图片URL",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageValidationFailed": "图片验证失败，请检查图片是否符合要求",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_UploadTheOriginalImage": "上传原始图片",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_YourCreativeDescription": "你的创意描述",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_SelectTheTemplateBelowOr": "选择下方模板，或自由输入",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_PictureSize": "图片尺寸",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_Generating": "生成中...",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_GenerateNow": "立即生成",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_HelpInformation": "帮助信息",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_AreYouSureYouWant": "您确定要删除此图片吗？",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_TheImageIsBeingGenerated": "正在根据你的要求生成图片...",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_CompletedPleaseCheck": "已完成，请查看",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_SorryTheGenerationFailed": "抱歉，生成失败了",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_TryAgain": "再试一次",
  "j-dingtalk-web_pages_ai-image_components_ImageList_NoDataAvailable": "暂无数据",
  "j-dingtalk-web_pages_ai-image_components_ImageList_StartCreatingYourFirstAi": "开始创建您的第一张AI图片吧！",
  "j-dingtalk-web_pages_ai-image_components_ImageList_CreateAnImage": "+ 创建图片",
  "j-dingtalk-web_pages_ai-image_components_ImageList_FailedToLoad": "加载失败",
  "j-dingtalk-web_pages_ai-image_components_ImageList_FailedToObtainTheImage": "获取图片列表失败，请重试",
  "j-dingtalk-web_pages_ai-image_components_ImageList_Retry": "重试",
  "j-dingtalk-web_pages_ai-image_components_ImageList_Loading": "加载中...",
  "j-dingtalk-web_pages_ai-image_components_ImageList_AllDataDisplayed": "已显示全部数据",
  "j-dingtalk-web_pages_ai-image_components_ImageList_SlideDownToLoadMore": "向下滑动加载更多",
  "j-dingtalk-web_pages_ai-image_components_WelcomeScreen_AiPortraitGeneration": "AI画像生成",
  "j-dingtalk-web_pages_ai-image_components_WelcomeScreen_TheBackgroundReplacementAndAutomatic": "背景替换和自动裁剪功能为您",
  "j-dingtalk-web_pages_ai-image_components_WelcomeScreen_GeneratePersonalizedHdImages": "生成个性化高清图片",
  "j-dingtalk-web_pages_ai-image_components_WelcomeScreen_StartCreating": "开始创作",
  "j-dingtalk-web_pages_ai-image_AiImageGeneration": "AI图像生成",
  "j-dingtalk-web_pages_ai-image_TheBackgroundReplacementAndAutomatic": "背景替换和自动裁剪功能为您生成个性化高清图片。",
  "j-dingtalk-web_pages_ai-image_FailedToObtainTheImage": "获取图片列表失败",
  "j-dingtalk-web_pages_ai-image_TheImageIsGeneratedSuccessfully": "图片生成成功！",
  "j-dingtalk-web_pages_ai-image_ImageGenerationFailed": "图片生成失败",
  "j-dingtalk-web_pages_ai-image_ImageGenerationFailedPleaseTry": "图片生成失败，请重试",
  "j-dingtalk-web_pages_ai-image_ImageGenerationTimedOutPlease": "图片生成超时，请重试",
  "j-dingtalk-web_pages_ai-image_ImageGenerationTakingLongerThan": "图片生成时间比预期更长，请耐心等待...",
  "j-dingtalk-web_pages_ai-image_UnknownStatusPleaseTryAgain": "未知状态，请重试",
  "j-dingtalk-web_pages_ai-image_FailedToCheckTheImage": "检查图片状态失败",
  "j-dingtalk-web_pages_ai-image_PleaseUploadAnImage": "请上传图片",
  "j-dingtalk-web_pages_ai-image_PleaseEnterACreativeDescription": "请输入创意描述",
  "j-dingtalk-web_pages_ai-image_PleaseSelectImageSize": "请选择图片尺寸",
  "j-dingtalk-web_pages_ai-image_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_ai-image_FailedToGeneratePleaseTry": "生成失败，请重试",
  "j-dingtalk-web_pages_ai-studio_AiMaterialDesign": "AI素材设计",
  "j-dingtalk-web_pages_ai-studio_AiImageGenerationAndAi": "AI图像生成和AI视频生成为您开启美好的创作之旅。",
  "j-dingtalk-web_pages_ai-studio_AiPortraitGeneration": "AI画像生成",
  "j-dingtalk-web_pages_ai-studio_TheBackgroundReplacementAndAutomatic": "背景替换和自动裁剪功能为您生成个性化高清图片",
  "j-dingtalk-web_pages_ai-studio_AiVideoGeneration": "AI视频生成",
  "j-dingtalk-web_pages_ai-studio_MakingHighQualityECommerce": "制作高质量的电商产品视频从未如此简单",
  "j-dingtalk-web_pages_aiVideo_components_WelcomeScreen_AiVideoGeneration": "AI视频生成",
  "j-dingtalk-web_pages_aiVideo_components_WelcomeScreen_ProduceHighQualityECommerce": "制作高质量的电商产品视频",
  "j-dingtalk-web_pages_aiVideo_components_WelcomeScreen_NeverBeenSoSimple": "从未如此简单",
  "j-dingtalk-web_pages_aiVideo_AiVideoGeneration": "AI视频生成",
  "j-dingtalk-web_pages_aiVideo_MakingHighQualityECommerce": "制作高质量的电商产品视频从未如此简单。",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_Share": "分享",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageSpecification": "图片规范",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ImageSize": "图片尺寸：",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_TheImageSizeCannotExceed": "1、图片大小不超过20MB；",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ResolutionGreaterThanLessThan": "2、分辨率大于512*512, 小于1600*1600。",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ShootingGuidance": "拍摄指导：",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_TheEdgeOfTheMain": "1、商品主体边缘清晰，避免模糊，商品尺寸过小；",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ThereIsNoWatermarkIn": "2、图片无水印，商品无遮挡；",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_TheBackgroundIsCleanAnd": "3、背景干净，白底图为佳，如背景过于杂乱，可能无法精确抠图。",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_CorrectExample": "正确示例",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ClearSubject": "主体清晰",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_CleanBackground": "背景干净",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ErrorExample": "错误示例",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_FuzzyBackground": "模糊、背景杂乱",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_TheProductBodyIsNot": "商品主体不清晰",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_ThisTimeWillConsume": "本操作将消耗1次",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_UsageCount": "可使用次数：",
  "j-dingtalk-web_pages_ai-video_components_VideoForm_ThisTimeWillConsume": "本操作将消耗2次",
  "j-dingtalk-web_pages_ai-video_components_VideoForm_UsageCount": "可使用次数：",
  "j-dingtalk-web_pages_ai-image_components_ImageList_Create": "新建",
  "j-dingtalk-web_pages_ai-image_Share": "分享",
  "j-dingtalk-web_pages_aiVideo_components_VideoForm_Share": "分享",
  "j-dingtalk-web_pages_aiVideo_components_VideoList_Create": "新建",
  "j-dingtalk-web_pages_aiVideo_Share": "分享",
  "j-dingtalk-web_pages_productOriginV2_components_ProductPage_Share": "分享",
  "j-dingtalk-web_pages_sourcing_components_Page_Share": "分享",
  "j-dingtalk-web_pages_aiVideo_components_ImageUploader_ReUpload": "重新上传",
  "j-dingtalk-web_pages_work-center_Detailed": "详细",
  "j-dingtalk-web_components_FreeModal_FreeGift": "免费赠送",
  "j-dingtalk-web_components_FreeModal_TrialCard": "10次体验卡",
  "j-dingtalk-web_components_FreeModal_ReceiveNow": "立即领取",
  "j-dingtalk-web_components_FreeModal_Receiving": "领取中...",
  "j-dingtalk-web_pages_ai-studio_ReceiveSuccess": "领取成功",
  "j-dingtalk-web_pages_ai-studio_OrderList": "订单列表",
  "j-dingtalk-web_pages_ai-image_components_ImageItem_FailedToGenerate": "生成失败",
  "j-dingtalk-web_pages_order-list_MyOrders": "我的订单",
  "j-dingtalk-web_pages_order-list_OrderPrice": "金额",
  "j-dingtalk-web_pages_order-list_OrderNumber": "订单编号",
  "j-dingtalk-web_pages_order-list_OrderTime": "下单时间",
  "j-dingtalk-web_pages_order-list_ProductPrice": "制品价格",
  "j-dingtalk-web_pages_order-list_PendingPayment": "待付款",
  "j-dingtalk-web_pages_order-list_UnderReview": "审查中",
  "j-dingtalk-web_pages_order-list_ReviewPassed": "审核通过",
  "j-dingtalk-web_pages_order-list_ReviewFailed": "审核未通过",
  "j-dingtalk-web_pages_order-list_OrderCanceled": "订单已取消",
  "j-dingtalk-web_pages_order-list_PendingPaymentSubtitle": "请在24小时内转账付款，并上传收据。否则将无法继续使用😊",
  "j-dingtalk-web_pages_order-list_UnderReviewSubtitle": "我们会尽快审核完成，再此期间您可正常使用",
  "j-dingtalk-web_pages_order-list_ReviewPassedSubtitle": "请尽情使用吧",
  "j-dingtalk-web_pages_order-list_ReviewFailedSubtitle": "您提供的信息不全，请重新购买",
  "j-dingtalk-web_pages_order-list_OrderCanceledSubtitle": "由于您未提供的支付凭证，此订单已取消，请重新购买",
  "j-dingtalk-web_pages_order-list_BankName": "银行名",
  "j-dingtalk-web_pages_order-list_Branch": "分行",
  "j-dingtalk-web_pages_order-list_AccountType": "账户类型",
  "j-dingtalk-web_pages_order-list_AccountNumber": "账户号码",
  "j-dingtalk-web_pages_order-list_AccountName": "收款账户名",
  "j-dingtalk-web_pages_order-list_TransferReceipt": "转账截图",
  "j-dingtalk-web_pages_order-list_ExampleReference": "样例参考",
  "j-dingtalk-web_pages_order-list_ReceiptTip": "请确认截图中包含账户名、卡号、金额等信息",
  "j-dingtalk-web_pages_order-list_UploadPhoto": "上传照片",
  "j-dingtalk-web_pages_order-list_SubmitInfo": "提交信息",
  "j-dingtalk-web_pages_order-list_CopySuccess": "{{label}}复制成功",
  "j-dingtalk-web_pages_order-list_CopyFailed": "复制失败",
  "j-dingtalk-web_pages_order-list_CopyBankInfo": "复制银行信息",
  "j-dingtalk-web_pages_order-list_CopyBankInfoSuccess": "银行信息复制成功",
  "j-dingtalk-web_pages_order-list_SubmitReceiptTip": "转账凭证提交功能开发中",
  "j-dingtalk-web_pages_order-list_NoImageUploaded": "请先上传转账截图",
  "j-dingtalk-web_pages_order-list_ReceiptSubmittedSuccess": "转账凭证提交成功",
  "j-dingtalk-web_pages_order-list_ReceiptSubmitFailed": "转账凭证提交失败",
  "j-dingtalk-web_pages_order-list_NetworkError": "网络错误，请重试",
  "j-dingtalk-web_pages_order-list_CancelFailed": "取消订单失败，请稍后重试",
  "j-dingtalk-web_pages_order-list_Submitting": "提交中...",
  "j-dingtalk-web_pages_create-order_Title": "充值",
  "j-dingtalk-web_pages_create-order_UsageCount": "可使用次数",
  "j-dingtalk-web_pages_create-order_Credits": "次",
  "j-dingtalk-web_pages_create-order_Description": "次的图像生成或{{videoCount}}次的视频生成",
  "j-dingtalk-web_pages_create-order_Feature1": "生成图像・生成视频的高速化",
  "j-dingtalk-web_pages_create-order_Feature2": "无有效期限，永久有效",
  "j-dingtalk-web_pages_create-order_Feature3": "不定期赠送次数",
  "j-dingtalk-web_pages_create-order_ProductPrice": "产品价格",
  "j-dingtalk-web_pages_create-order_DiscountPrice": "优惠价格",
  "j-dingtalk-web_pages_create-order_Currency": "元",
  "j-dingtalk-web_pages_create-order_PaymentButton": "先使用，后付费",
  "common.usageCount": "可使用次数",
  "j-dingtalk-web_components_FeaturePromotion_Usage": "可使用次数：",
  "j-dingtalk-web_components_FreeModal_By": "由",
  "j-dingtalk-web_components_FreeModal_Official": "官方",
  "j-dingtalk-web_components_FreeModal_Gift": "赠送",
  "j-dingtalk-web_components_PcHeaderActions_TheLinkHasBeenCopied": "链接已复制到剪贴板",
  "j-dingtalk-web_components_PcHeaderActions_ReplicationFailedPleaseCopyThe": "复制失败，请手动复制链接",
  "j-dingtalk-web_pages_ai-image_components_ImageForm_SpecificationReference": "规范参考",
  "j-dingtalk-web_pages_ai-image_FailedToObtainQuotaUsage": "获取配额使用情况失败",
  "j-dingtalk-web_pages_ai-image_FailedToObtainFreeQuota": "获取免费额度失败",
  "j-dingtalk-web_pages_ai-image_TheInterfaceIsAbnormalPlease": "接口异常，请稍后再试~",
  "j-dingtalk-web_pages_ai-image_FailedToCollectPleaseTry": "领取失败，请稍后再试",
  "j-dingtalk-web_pages_ai-image_List": "列表",
  "j-dingtalk-web_pages_ai-studio_FailedToObtainQuotaUsage": "获取配额使用情况失败",
  "j-dingtalk-web_pages_ai-studio_FailedToObtainFreeQuota": "获取免费额度失败",
  "j-dingtalk-web_pages_ai-studio_TheInterfaceIsAbnormalPlease": "接口异常，请稍后再试~",
  "j-dingtalk-web_pages_ai-studio_FailedToCollectPleaseTry": "领取失败，请稍后再试",
  "j-dingtalk-web_pages_ai-video_components_VideoForm_SpecificationReference": "规范参考",
  "j-dingtalk-web_pages_ai-video_FailedToObtainQuotaUsage": "获取配额使用情况失败",
  "j-dingtalk-web_pages_ai-video_FailedToObtainFreeQuota": "获取免费额度失败",
  "j-dingtalk-web_pages_ai-video_TheInterfaceIsAbnormalPlease": "接口异常，请稍后再试~",
  "j-dingtalk-web_pages_ai-video_FailedToCollectPleaseTry": "领取失败，请稍后再试",
  "j-dingtalk-web_pages_ai-video_List": "列表",
  "j-dingtalk-web_pages_create-order_TheOrderHasBeenGenerated": "订单生成成功，请及时付款",
  "j-dingtalk-web_pages_create-order_OrderGenerationFailedPleaseTry": "订单生成失败，请重试",
  "j-dingtalk-web_pages_create-order_LiZhong": "处理中...",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_TheNumberOfUsageHas": "可使用次数已分配！",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_OrderDetails": "订单详情",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_BankBank": "みずほ銀行（銀行コード 0001）",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_NoBranchStoreBranchStore": "十八号支店（支店コード 986）",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_AsASeat": "当座",
  "j-dingtalk-web_pages_order-list_FailedToGetTheOrder": "获取订单列表失败",
  "j-dingtalk-web_pages_order-list_NetworkErrorPleaseTryAgain": "网络错误，请稍后重试",
  "j-dingtalk-web_pages_order-list_FailedToObtainOrderDetails": "获取订单详情失败",
  "j-dingtalk-web_pages_order-list_Retry": "重试",
  "j-dingtalk-web_pages_order-list_NoPurchaseHistory": "暂无购买历史记录",
  "j-dingtalk-web_pages_order-list_PleaseSelectAnOrderTo": "请选择一个订单查看详情",
  "j-dingtalk-web_pages_order-list_utils_PendingPayment": "待支付",
  "j-dingtalk-web_pages_order-list_utils_UnderReview": "审核中",
  "j-dingtalk-web_pages_order-list_utils_Approved": "审核通过",
  "j-dingtalk-web_pages_order-list_utils_ReviewFailed": "审核失败",
  "j-dingtalk-web_pages_order-list_utils_UnknownStatus": "未知状态",
  "j-dingtalk-web_pages_order-list_components_OrderDetail_Delete": "删除",
  "j-dingtalk-web_pages_order-list_AllOrdersDisplayed": "已显示全部订单",
  "j-dingtalk-web_pages_create-order_TaxPrice": "消费税",
  "j-dingtalk-web_pages_create-order_TotalPrice": "合计",
  "j-dingtalk-web_pages_order-list_RePurchase": "重新购买",
  "j-dingtalk-web_pages_ai-image_InvalidImageUrl": "无效的图片链接",
  "j-dingtalk-web_pages_ai-image_ProcessingImage": "正在处理图片以获得最佳效果...",
  "j-dingtalk-web_pages_ai-image_ImagePreprocessingFailed": "图片预处理失败，使用原图",
  "j-dingtalk-web_pages_ai-image_ImageUploadFailed": "处理后图片上传失败，使用原图",
  "j-dingtalk-web_pages_create-order_Payment": "支付"
};
