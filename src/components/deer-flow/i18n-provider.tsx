// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

'use client';

import { NextIntlClientProvider } from 'next-intl';
import { useEffect, useState } from 'react';

interface I18nProviderProps {
  children: React.ReactNode;
}

export function I18nProvider({ children }: I18nProviderProps) {
  const [messages, setMessages] = useState<any>({});
  const [locale, setLocale] = useState('en');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Get locale from localStorage or browser language
    const getInitialLocale = () => {
      if (typeof window === 'undefined') return 'en';
      
      // Try to get from localStorage first
      const savedLocale = localStorage.getItem('NEXT_LOCALE');
      if (savedLocale && ['zh', 'en'].includes(savedLocale)) {
        return savedLocale;
      }
      
      // Fallback to browser language
      const browserLocale = navigator.language.startsWith('zh') ? 'zh' : 'en';
      return browserLocale;
    };

    const loadMessages = async () => {
      const currentLocale = getInitialLocale();
      setLocale(currentLocale);
      
      try {
        const messages = await import(`../../../messages/${currentLocale}.json`);
        setMessages(messages.default);
      } catch (error) {
        console.warn(`Failed to load messages for locale ${currentLocale}, falling back to English`);
        const fallbackMessages = await import('../../../messages/en.json');
        setMessages(fallbackMessages.default);
      } finally {
        setIsLoading(false);
      }
    };

    loadMessages();
  }, []);

  // Show loading or return provider with messages
  if (isLoading) {
    return <div>{children}</div>; // Return children without i18n during loading
  }

  return (
    <NextIntlClientProvider messages={messages} locale={locale}>
      {children}
    </NextIntlClientProvider>
  );
}
