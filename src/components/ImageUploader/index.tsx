import { i18next } from '@ali/dingtalk-i18n';
import React, { useState, useEffect, useRef } from 'react';
import { Button, Toast } from 'dingtalk-design-mobile';
import $uploadImage from '@ali/dingtalk-jsapi/api/biz/util/uploadImage';
import $saveImage from '@ali/dingtalk-jsapi/api/biz/util/saveImage';
import uploadWebImage from '@ali/dd-upload';
import { AddToSFilled } from '@ali/ding-icons';
import { previewImages } from '@/components/imagesPreview';
import { isDingTalk, isMobileDevice } from '@/utils/jsapi';
import { log } from '@/utils/console';
import { sendUT } from '@/utils/trace';
import request from '@/apis/base';
import './index.less';

interface ImageSizeConstraints {
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

interface ImageUploaderProps {
  value?: string | null;
  onChange?: (imageUrl: string | null) => void;
  sizeConstraints?: ImageSizeConstraints; // 图片尺寸限制配置
  scanningLoading?: boolean; // 抠图扫描加载状态
  hasCutBackground?: boolean; // 是否需要抠图背景
  uploadText?: string; // 上传文本
  showReuploadBtn?: boolean; // 是否显示重新上传按钮
  disabled?: boolean; // 是否禁用上传功能
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  value,
  onChange,
  sizeConstraints, // 不设置默认值，如果没有传递则不校验
  scanningLoading = false, // 抠图扫描加载状态
  hasCutBackground = false, // 是否需要抠图背景
  uploadText = '', // 上传文本
  showReuploadBtn = true, // 是否显示重新上传按钮
  disabled = false, // 是否禁用上传功能
}) => {
  const [loading, setLoading] = useState(false);
  const uploadAreaRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null); // For web file input

  const isMobile = isMobileDevice();

  // Auto focus on upload area when component mounts and no image is uploaded
  useEffect(() => {
    if (!value && uploadAreaRef.current) {
      // Set focus on the upload area for accessibility
      uploadAreaRef.current.focus();
    }
  }, [value]);

  // 保存图片到本地的函数 - Save image to local storage using DingTalk native API
  const saveImageToLocal = (imageUrl: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      $saveImage({
        image: imageUrl,
      })
        .then(() => {
          resolve();
        })
        .catch((error: Error) => {
          // eslint-disable-next-line no-console
          log.error('Failed to save image to local:', error);
          reject(error);
        });
    });
  };

  // 验证和处理图片尺寸的函数 - Validate and process image dimensions
  const validateImageSize = (
    imageUrl: string,
  ): Promise<{ isValid: boolean; processedUrl: string }> => {
    return new Promise((resolve) => {
      // 如果没有传递尺寸限制参数，则跳过验证 - Skip validation if no size constraints provided
      if (!sizeConstraints) {
        resolve({ isValid: true, processedUrl: imageUrl });
        return;
      }

      const img = new Image();
      img.onload = () => {
        const { width, height } = img;
        const {
          minWidth = 0,
          minHeight = 0,
          maxWidth = Infinity,
          maxHeight = Infinity,
        } = sizeConstraints;

        // 检查图片是否小于最小尺寸 - Check if image is smaller than minimum size
        if (width < minWidth || height < minHeight) {
          // 图片过小，自动添加放大参数 - Image too small, add scaling parameters to enlarge
          let processedUrl = imageUrl;
          // 判断是宽度还是高度需要放大更多 - Determine which dimension needs more enlargement
          const widthRatio = minWidth / width;
          const heightRatio = minHeight / height;
          // 根据需要放大比例更大的一边进行缩放 - Scale based on the dimension that needs more enlargement
          let resizeParams = '';
          if (widthRatio >= heightRatio) {
            // 宽度需要放大更多，按宽度放大 - Width needs more enlargement, scale by width
            resizeParams = `_${minWidth}x10000.jpg`;
          } else {
            // 高度需要放大更多，按高度放大 - Height needs more enlargement, scale by height
            resizeParams = `_10000x${minHeight}.jpg`;
          }
          // 处理URL，添加放大参数 - Process URL, add scaling parameters
          // 如果URL已经包含参数，需要特殊处理 - Handle URLs that already contain parameters
          if (processedUrl.includes('?')) {
            // URL包含查询参数，在查询参数前插入缩放参数
            const [baseUrl, queryParams] = processedUrl.split('?');
            processedUrl = `${baseUrl}${resizeParams}?${queryParams}`;
          } else {
            // URL不包含查询参数，直接添加缩放参数
            processedUrl = `${processedUrl}${resizeParams}`;
          }
          resolve({ isValid: true, processedUrl });
          return;
        }

        // 检查图片是否大于最大尺寸 - Check if image is larger than maximum size
        if (width > maxWidth || height > maxHeight) {
          // 图片过大，自动添加缩放参数 - Image too large, add scaling parameters
          let processedUrl = imageUrl;
          // 判断是宽度还是高度超出更多 - Determine which dimension exceeds more
          const widthRatio = width / maxWidth;
          const heightRatio = height / maxHeight;
          // 根据超出比例更大的一边进行缩放 - Scale based on the dimension that exceeds more
          let resizeParams = '';
          if (widthRatio >= heightRatio) {
            // 宽度超出更多，按宽度缩放 - Width exceeds more, scale by width
            resizeParams = `_${maxWidth}x10000.jpg`;
          } else {
            // 高度超出更多，按高度缩放 - Height exceeds more, scale by height
            resizeParams = `_10000x${maxHeight}.jpg`;
          }
          // 处理URL，添加缩放参数 - Process URL, add scaling parameters
          // 如果URL已经包含参数，需要特殊处理 - Handle URLs that already contain parameters
          if (processedUrl.includes('?')) {
            // URL包含查询参数，在查询参数前插入缩放参数
            const [baseUrl, queryParams] = processedUrl.split('?');
            processedUrl = `${baseUrl}${resizeParams}?${queryParams}`;
          } else {
            // URL不包含查询参数，直接添加缩放参数
            processedUrl = `${processedUrl}${resizeParams}`;
          }
          resolve({ isValid: true, processedUrl });
          return;
        }

        // 图片尺寸符合要求 - Image dimensions meet requirements
        resolve({ isValid: true, processedUrl: imageUrl });
      };
      img.onerror = () => {
        Toast.fail({
          content: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageLoadError',
          ),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
        resolve({ isValid: false, processedUrl: imageUrl });
      };
      img.src = imageUrl;
    });
  };

  const handleImageClick = () => {
    if (value) {
      // Add timestamp to prevent caching issues with processed images
      const previewUrl = value.includes('?')
        ? `${value}&t=${Date.now()}`
        : `${value}?t=${Date.now()}`;

      previewImages({
        photos: [{ src: previewUrl }],
        current: 0,
      });
    }
  };

  const handleReupload = (e: React.MouseEvent) => {
    sendUT('aigc_picture_reupdate', {
      device: isMobile ? 'mobile' : 'pc',
      imageUrl: value,
    });

    e.stopPropagation(); // Prevent triggering image click
    handleUploadClick();
  };

  // Handle file selection for web environment
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_OnlyImageFilesAllowed',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    // Check file size (limit to 30MB)
    const maxSize = 30 * 1024 * 1024; // 30MB
    if (file.size > maxSize) {
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_FileTooLarge',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
      return;
    }

    setLoading(true);

    try {
      // Upload file using @ali/dd-upload
      const uploadResult = await uploadWebImage({
        file,
        lwp: true,
        lwpRequest: (url, headers, body) => {
          return request(
            url,
            body,
            headers,
          );
        },
        id: 'dingRichTextEditor',
        downloadId: 'dingRichTextEditor',
      });

      if (uploadResult && uploadResult.originUrl) {
        // Validate and process image dimensions
        const validationResult = await validateImageSize(uploadResult.originUrl);

        if (validationResult.isValid) {
          // Use processed URL (may include scaling parameters)
          onChange?.(validationResult.processedUrl);

          Toast.success({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully',
            ),
            position: 'top',
            maskClickable: true,
            duration: 2,
          });
        }
      } else {
        Toast.fail({
          content: i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
          ),
          position: 'top',
          maskClickable: true,
          duration: 3,
        });
      }
    } catch (error) {
      log.error('Web upload failed:', error);
      Toast.fail({
        content: i18next.t(
          'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
        ),
        position: 'top',
        maskClickable: true,
        duration: 3,
      });
    } finally {
      setLoading(false);
      // Clear the input value so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleUploadClick = async () => {
    if (!isDingTalk()) {
      // Trigger file input for web environment
      fileInputRef.current?.click();
      return;
    }

    setLoading(true);
    $uploadImage({
      multiple: false, // Single image upload
      compression: true, // Enable image compression
      max: 1, // Maximum 1 image
    })
      .then(async (result: any) => {
        log.info('upload result', result);
        // Handle upload success
        if (result && result.length > 0) {
          // Get the first uploaded image result
          const uploadResult = result[0];
          // Check if result is a string (image URL) or object with localId (camera mode)
          let imageUrl: string;

          if (typeof uploadResult === 'string') {
            // Direct image URL from gallery
            imageUrl = uploadResult;
          } else if (uploadResult && typeof uploadResult === 'object') {
            // Object result, might contain localId for camera mode
            imageUrl = uploadResult.url || uploadResult.mediaId || uploadResult;
          } else {
            imageUrl = uploadResult;
          }

          // 验证和处理图片尺寸 - Validate and process image dimensions
          const validationResult = await validateImageSize(imageUrl);
          if (validationResult.isValid) {
            // 使用处理后的图片URL（可能包含缩放参数）- Use processed URL (may include scaling parameters)
            onChange?.(validationResult.processedUrl);

            // 如果是移动端，保存图片到本地
            if (isDingTalk() && isMobileDevice()) {
              try {
                await saveImageToLocal(validationResult.processedUrl);
              } catch (error) {
                // eslint-disable-next-line no-console
                log.warn('Failed to save camera image to local:', error);
              }
            }

            Toast.success({
              content: i18next.t(
                'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadedSuccessfully',
              ),
              position: 'top',
              maskClickable: true,
              duration: 2,
            });
          }
        } else {
          Toast.fail({
            content: i18next.t(
              'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ImageUploadFailed',
            ),
            position: 'top',
            maskClickable: true,
            duration: 3,
          });
        }
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  return (
    <div className="image-uploader">
      {value ? (
        <div className="uploaded-image" onClick={handleImageClick}>
          {hasCutBackground ? (
            <div className="preview-img-container">
              <img key={value} src={value} alt="Uploaded" className="preview-img" />
            </div>
          ) : (
            <img key={value} src={value} alt="Uploaded" className="preview-img" />
          )}
          {
            showReuploadBtn && (
              <div className="image-actions">
                <Button size="small" onClick={handleReupload} className="action-btn reupload-btn">
                  {i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_ReUpload')}
                </Button>
              </div>
            )
          }
        </div>
      ) : (
        <div
          ref={uploadAreaRef}
          className={`upload-area ${disabled ? 'disabled' : ''}`}
          onClick={disabled ? undefined : handleUploadClick}
          tabIndex={disabled ? -1 : 0}
          role="button"
          aria-label={i18next.t(
            'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage',
          )}
          aria-disabled={disabled}
        >
          {!loading && (
            <>
              <AddToSFilled className="upload-icon" />
              <div className="upload-text">
                {uploadText || i18next.t(
                  'j-dingtalk-web_pages_aiVideo_components_ImageUploader_ClickUploadImage',
                )}
              </div>
            </>
          )}
          {scanningLoading && (
            <div className="scanning-overlay">
              <div className="scanning-line" />
            </div>
          )}
        </div>
      )}
      {loading && (
        <div className="upload-loading">
          <div className="loading-spinner" />
          <span>
            {i18next.t('j-dingtalk-web_pages_aiVideo_components_ImageUploader_Uploading')}
          </span>
        </div>
      )}

      {/* Hidden file input for web environment */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
      />
    </div>
  );
};

export default ImageUploader;
