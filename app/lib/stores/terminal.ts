import type { WebContainer, ProcProxy } from '~/lib/webcontainer';
import { atom, type WritableAtom } from 'nanostores';
import type { ITerminal } from '~/types/terminal';
import { execCommandInShell, newAIBuilderShellProcess, newShellProcess } from '~/utils/shell';
import { coloredText } from '~/utils/terminal';
import { Deferred, isPromisePending, withResolvers } from '~/utils/promises';

export class TerminalStore {
  #webcontainer: Promise<WebContainer>;
  #terminals: Array<{ terminal: ITerminal; process: ProcProxy }> = [];
  #terminal = new Deferred<{ terminal: ITerminal; process: ProcProxy }>();
  #aibuilderTerminal = newAIBuilderShellProcess();

  showTerminal: WritableAtom<boolean> = import.meta.hot?.data.showTerminal ?? atom(true);

  constructor(webcontainerPromise: Promise<WebContainer>) {
    this.#webcontainer = webcontainerPromise;

    if (import.meta.hot) {
      import.meta.hot.data.showTerminal = this.showTerminal;
    }
  }
  get aibuilderTerminal() {
    return this.#aibuilderTerminal;
  }

  toggleTerminal(value?: boolean) {
    this.showTerminal.set(value !== undefined ? value : !this.showTerminal.get());
  }

  async attachAIBuilderTerminal(terminal: ITerminal) {
    try {
      const wc = await this.#webcontainer;
      await this.#aibuilderTerminal.init(wc, terminal);
    } catch (error: any) {
      terminal.write(coloredText.red('Failed to spawn AIBuilder shell\n\n') + error.message);
      return;
    }
  }

  async attachTerminal(terminal: ITerminal) {
    try {
      const shellProcess = await newShellProcess(await this.#webcontainer, terminal);
      this.#terminals.push({ terminal, process: shellProcess });

      if (await isPromisePending(this.#terminal.promise)) {
        this.#terminal.resolve({ terminal, process: shellProcess! });
      }
    } catch (error: any) {
      terminal.write(coloredText.red('Failed to spawn shell\n\n') + error.message);
      return;
    }
  }

  async exec(command: string) {
    const terminal = await this.#terminal.promise;
    const webcontainer = await this.#webcontainer;
    const process = await execCommandInShell(webcontainer, command, terminal.terminal);

    const { promise, resolve, reject } = withResolvers();

    process?.on('exit', (code, sig) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(code, sig);
      }
    });

    return promise;
  }

  onTerminalResize(cols: number, rows: number) {
    for (const { process } of this.#terminals) {
      // process.resize({ cols, rows });
    }
  }

  async detachTerminal(terminal: ITerminal) {
    const terminalIndex = this.#terminals.findIndex((t) => t.terminal === terminal);

    if (terminalIndex !== -1) {
      const { process } = this.#terminals[terminalIndex];

      try {
        process.kill(2);
      } catch (error) {
        console.warn('Failed to kill terminal process:', error);
      }
      this.#terminals.splice(terminalIndex, 1);
    }
  }
}
