import { BaseProvider } from '~/lib/modules/llm/base-provider';
import type { ModelInfo } from '~/lib/modules/llm/types';
import type { IProviderSetting } from '~/types/model';
import type { LanguageModelV1 } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';

export default class AIStudioProvider extends BaseProvider {
  name = 'AIStudio';
  getApiKeyLink = 'https://idealab.alibaba-inc.com/ideaTalk#/aistudio/manage/accessKeyManage';

  config = {
    apiTokenKey: 'AISTUDIO_API_KEY',
  };

  staticModels: ModelInfo[] = [
    { name: 'qwen3-coder-plus', label: 'qwen3-coder-plus', provider: 'AIStudio', maxTokenAllowed: 8000 },
  ];

  getModelInstance(options: {
    model: string;
    serverEnv: Env;
    apiKeys?: Record<string, string>;
    providerSettings?: Record<string, IProviderSetting>;
  }): LanguageModelV1 {
    const { model, serverEnv, apiKeys, providerSettings } = options;

    const { apiKey } = this.getProviderBaseUrlAndKey({
      apiKeys,
      providerSettings: providerSettings?.[this.name],
      serverEnv: serverEnv as any,
      defaultBaseUrlKey: '',
      defaultApiTokenKey: 'AISTUDIO_API_KEY',
    });

    if (!apiKey) {
      throw new Error(`Missing API key for ${this.name} provider`);
    }

    const openai = createOpenAI({
      apiKey,
      baseURL: 'https://idealab.alibaba-inc.com/api/openai/v1',
    });

    return openai(model);
  }
}
