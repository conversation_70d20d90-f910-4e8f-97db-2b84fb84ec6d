// 已统一导出 webc 相关内容，无需再从 webc-api 导入
import { WORK_DIR } from '~/utils/constants';
import type { WebContainer } from './webc';
import { WebCAdapter } from './webc';

interface WebContainerContext {
  loaded: boolean;
}

export const webcontainerContext: WebContainerContext = import.meta.hot?.data.webcontainerContext ?? {
  loaded: false,
};

if (import.meta.hot) {
  import.meta.hot.data.webcontainerContext = webcontainerContext;
}

export * from './webc';

export let webcontainer: Promise<WebContainer> = new Promise(() => {
  // noop for ssr
});

if (!import.meta.env.SSR) {
  webcontainer =
    import.meta.hot?.data.webcontainer ??
    Promise.resolve()
      .then(() => {
        const instance = new WebCAdapter();
        return instance.boot({
          coep: 'credentialless',
          workdirName: WORK_DIR,
          forwardPreviewErrors: true, // Enable error forwarding from iframes
        });
      })
      .then(async (webcontainer) => {
        webcontainerContext.loaded = true;

        // const { workbenchStore } = await import('~/lib/stores/workbench');

        // const response = await fetch('/inspector-script.js');
        // const inspectorScript = await response.text();
        // await webcontainer.setPreviewScript(inspectorScript);

        // // Listen for preview errors
        // webcontainer.on('preview-message', (message: any) => {
        //   console.log('WebContainer preview message:', message);

        //   // Handle both uncaught exceptions and unhandled promise rejections
        //   if (message.type === 'PREVIEW_UNCAUGHT_EXCEPTION' || message.type === 'PREVIEW_UNHANDLED_REJECTION') {
        //     const isPromise = message.type === 'PREVIEW_UNHANDLED_REJECTION';
        //     const title = isPromise ? 'Unhandled Promise Rejection' : 'Uncaught Exception';
        //     workbenchStore.actionAlert.set({
        //       type: 'preview',
        //       title,
        //       description: 'message' in message ? message.message : 'Unknown error',
        //       content: `Error occurred at ${message.pathname}${message.search}${message.hash}\nPort: ${message.port}\n\nStack trace:\n${cleanStackTrace(message.stack || '')}`,
        //       source: 'preview',
        //     });
        //   }
        // });

        return webcontainer;
      });

  if (import.meta.hot) {
    import.meta.hot.data.webcontainer = webcontainer;
  }
}
