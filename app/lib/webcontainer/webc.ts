import type { IWebC, EnvEnum } from '@ali/wasm-webc-sys';
import type { IFSApi } from '@ali/wasm-webc-sys/webc/common/fs';
import { path as nodePath } from '~/utils/path';
import type { Proc<PERSON>anager, ProcProxy } from '@ali/wasm-webc-sys/webc/webk/proc';

export type { ProcProxy };

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum FS_EVENTS_ENUMS {
  IN_ACCESS = 0,
  IN_ATTRIB = 1,
  IN_CLOSE_WRITE = 2,
  IN_CLOSE_NOWRITE = 3,
  IN_CREATE = 4,
  IN_DELETE = 5,
  IN_DELETE_SELF = 6,
  IN_MODIFY = 7,
  IN_MOVE_SELF = 8,
  IN_MOVED_FROM = 9,
  IN_MOVED_TO = 10,
  IN_OPEN = 11,
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum FILE_TYPE {
  UNKOWN = 0,
  DATA_FILE = 1,
  DIRECTORY = 2,
  SYMBOLIC = 3,
}

export interface WebContainerOptions {
  coep?: 'credentialless' | 'require-corp';
  workdirName?: string;
  forwardPreviewErrors?: boolean;
}
export interface TerminalOptions {
  cols: number;
  rows: number;
}
export interface SpawnOptions {
  terminal?: TerminalOptions;
  env?: Record<string, string>;
  cwd?: string;
}
export interface TextSearchOptions {
  folders?: string[];
  homeDir?: string;
  includes?: string[];
  excludes?: string[];
  gitignore?: boolean;
  requireGit?: boolean;
  globalIgnoreFiles?: boolean;
  ignoreSymlinks?: boolean;
  resultLimit?: number;
  isRegex?: boolean;
  caseSensitive?: boolean;
  isWordMatch?: boolean;
}
export type TextSearchOnProgressCallback = (filePath: string, matches: any[]) => void;
export interface PathWatcherEvent {
  type: 'create' | 'change' | 'delete' | 'add_file' | 'remove_file' | 'add_dir' | 'remove_dir';
  path: string;
  buffer?: Uint8Array;
}
export interface WebContainerFS {
  mkdir(path: string, options?: { recursive?: boolean }): Promise<void>;
  readFile(path: string, encoding?: string): Promise<string>;
  writeFile(path: string, content: string): Promise<void>;
  unlink(path: string): Promise<void>;
  rmdir(path: string): Promise<void>;
  rename(oldPath: string, newPath: string): Promise<void>;
  readdir(path: string): Promise<[string, FILE_TYPE][]>;
  stat(path: string): Promise<{
    isFile(): boolean;
    isDirectory(): boolean;
    size: number;
    mtime: Date;
  }>;
  copy(src: string, dest: string): Promise<void>;
  symlink(target: string, path: string): Promise<void>;
  watch(path: string, callback: (event: PathWatcherEvent) => void): Promise<number>;
  unwatch(watchId: string): Promise<void>;
}
export interface WatchPathsOptions {
  include: string[];
  exclude?: string[];
  includeContent?: boolean;
}
export interface WebContainer {
  boot(options?: WebContainerOptions): Promise<WebContainer>;
  setPreviewScript(script: string): Promise<void>;
  workdir: string;
  fs: WebContainerFS;
  originalFs: IFSApi;
  proc: typeof ProcManager;
  internal: {
    textSearch?: (query: string, options: TextSearchOptions, callback: TextSearchOnProgressCallback) => Promise<void>;
    watchPaths: (options: WatchPathsOptions, callback: (event: PathWatcherEvent) => void) => void;
  };
  on(event: string, listener: (...args: any[]) => void): this;
  off(event: string, listener: (...args: any[]) => void): this;
  emit(event: string, ...args: any[]): boolean;
}

const isClient = typeof window !== 'undefined';

export class WebCAdapter implements WebContainer {
  private _webc!: IWebC;
  private _fsApi!: IFSApi;
  private _procManager!: typeof ProcManager;
  private _isBooted = false;
  private _previewScript?: string;
  private _eventEmitter: EventTarget;
  private _workdir: string = '/';
  private _fs: WebContainerFS;
  private _portManagerInitialized = false;
  private _listeners: Map<string, Map<(...args: any[]) => void, (event: Event) => void>> = new Map();

  private _resolvePath(path: string): string {
    return path.startsWith('/') ? path : nodePath.join(this._workdir, path);
  }

  constructor() {
    if (!isClient) {
      throw new Error('WebCAdapter 只能在浏览器环境下使用');
    }

    this._eventEmitter = new EventTarget();
    this._fs = {
      mkdir: async (path: string, options?: { recursive?: boolean }) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        // 判断目录不存在
        const folderData = await this._fsApi.readdir(path);

        if (!folderData?.data || !folderData?.data.length) {
          const absPath = this._resolvePath(path);
          const result = await this._fsApi.mkdir(absPath, 0o777, options?.recursive || false);

          if (result.errno !== 0) {
            throw new Error(`Failed to create directory: ${result.code || 'Unknown error'}`);
          }
        }

        return;
      },
      readFile: async (path: string, _encoding?: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.readfile(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to read file: ${result.code || 'Unknown error'}`);
        }

        return new TextDecoder().decode(result.data);
      },
      writeFile: async (path: string, content: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.writefile(absPath, content);

        // -29 有异常，临时放过，但是警告
        if (result.errno === -29) {
          console.warn(`write file: ${result.code || 'Unknown error'}`);
        }

        if (result.errno !== 0 && result.errno !== -29) {
          throw new Error(`Failed to write file: ${result.code || 'Unknown error'}`);
        }
      },
      unlink: async (path: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.unlink(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to delete file: ${result.code || 'Unknown error'}`);
        }
      },
      rmdir: async (path: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.rmdir(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to delete directory: ${result.code || 'Unknown error'}`);
        }
      },
      rename: async (oldPath: string, newPath: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absOldPath = this._resolvePath(oldPath);
        const absNewPath = this._resolvePath(newPath);
        const result = await this._fsApi.rename(absOldPath, absNewPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to rename: ${result.code || 'Unknown error'}`);
        }
      },
      readdir: async (path: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.readdir(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to read directory: ${result.code || 'Unknown error'}`);
        }

        return result?.data || [];
      },
      stat: async (path: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.stat(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to stat: ${result.code || 'Unknown error'}`);
        }

        return {
          isFile: () => result.data?.type === 1,
          isDirectory: () => result.data?.type === 2,
          size: 0,
          mtime: new Date(result.data?.mtime || 0),
        };
      },
      copy: async (src: string, dest: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absSrc = this._resolvePath(src);
        const absDest = this._resolvePath(dest);
        const result = await this._fsApi.copy(absSrc, absDest);

        if (result.errno !== 0) {
          throw new Error(`Failed to copy file: ${result.code || 'Unknown error'}`);
        }
      },
      symlink: async (target: string, path: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.symlink(target, absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to create symlink: ${result.code || 'Unknown error'}`);
        }
      },
      watch: async (path: string, _callback: (event: PathWatcherEvent) => void) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const absPath = this._resolvePath(path);
        const result = await this._fsApi.watch(absPath);

        if (result.errno !== 0) {
          throw new Error(`Failed to watch path: ${result.code || 'Unknown error'}`);
        }

        return result.data || 0;
      },
      unwatch: async (watchId: string) => {
        if (!this._isBooted) {
          throw new Error('WebC system not booted');
        }

        const result = await this._fsApi.unwatch(watchId);

        if (result.errno !== 0) {
          throw new Error(`Failed to unwatch: ${result.code || 'Unknown error'}`);
        }
      },
    };
  }

  get workdir(): string {
    return this._workdir;
  }
  get fs(): WebContainerFS {
    return this._fs;
  }

  get originalFs(): IFSApi {
    return this._webc.getFSApi();
  }

  get proc(): typeof ProcManager {
    return this._procManager;
  }

  async boot(options?: WebContainerOptions): Promise<WebContainer> {
    if (this._isBooted) {
      return this;
    }

    try {
      this._webc = (await import('@ali/wasm-webc-sys')).sys;

      await this._webc.boot({
        coep: options?.coep === 'credentialless',
        env: 'prod' as EnvEnum,
        withNode: true,
        openUrl: (url) => {
          console.log('open url ', url);
          window.open(url, '_blank');

          return true;
        },
        wsHttpProxyUrl: 'https://webc-net-helper.alibaba-inc.com',

        // @ts-ignore
        wsHttpProxyConnectionSize: 1,
        logLevel: 3,
        onSysEvent: (ev) => {
          console.log('webc boot onSysEvent', JSON.stringify(ev));
        },
      });

      if (options?.workdirName) {
        this._workdir = `${options.workdirName}`;
      }

      this._fsApi = this._webc.getFSApi();
      this._procManager = this._webc.getProcManager();

      const installer = this._webc.getInstallerManager();

      const fs = this._webc.getFSApi();
      fs.mkdir(this._workdir, 0o777, true);
      await installer.installBins([
        'ls',
        'bash',
        'git',
        'cat',
        'sleep',
        'sort',
        'md5sum',
        'mkdir',
        'echo',
        'rm',
        'cp',
        'mv',
        'env',
      ]);
      fs.symlink('/bin/bash', '/bin/sh');
      fs.mkdir('/usr/bin', 0o777, true);
      fs.copy('/bin/env', '/usr/bin/env');

      await installer.initNpm('https://registry.anpm.alibaba-inc.com');

      if (!this._portManagerInitialized && this._procManager?.portManager) {
        const instanceId = this._webc.getInstanceId?.();
        this._procManager.portManager.on('listenStart', (port: number) => {
          const url = instanceId ? `${window.location.origin}/_i/${instanceId}/_p/${port}/` : '';
          this.emit('port', port, 'open', url);
        });
        this._procManager.portManager.on('listenStop', (port: number) => {
          this.emit('port', port, 'close', '');
        });
        this._portManagerInitialized = true;
      }

      this._isBooted = true;

      return this;
    } catch (error) {
      console.error('Failed to boot WebC system:', error);
      throw error;
    }
  }

  async setPreviewScript(script: string): Promise<void> {
    this._previewScript = script;

    // 可扩展：WebC 中预览脚本的处理逻辑
  }

  on(event: string, listener: (...args: any[]) => void): this {
    // 创建包装函数
    const wrappedListener = (event: Event) => {
      const customEvent = event as CustomEvent;
      listener(...(customEvent.detail as any[]));
    };

    // 保存监听器映射
    if (!this._listeners.has(event)) {
      this._listeners.set(event, new Map());
    }

    this._listeners.get(event)!.set(listener, wrappedListener);

    // 添加事件监听器
    this._eventEmitter.addEventListener(event, wrappedListener);

    return this;
  }

  off(event: string, listener: (...args: any[]) => void): this {
    const eventListeners = this._listeners.get(event);

    if (eventListeners) {
      const wrappedListener = eventListeners.get(listener);

      if (wrappedListener) {
        this._eventEmitter.removeEventListener(event, wrappedListener);
        eventListeners.delete(listener);

        // 如果没有更多监听器，清理事件映射
        if (eventListeners.size === 0) {
          this._listeners.delete(event);
        }
      }
    }

    return this;
  }

  emit(event: string, ...args: any[]): boolean {
    const customEvent = new CustomEvent(event, { detail: args });
    return this._eventEmitter.dispatchEvent(customEvent);
  }
  internal = {
    textSearch: async (_query: string, _options: TextSearchOptions, _callback: TextSearchOnProgressCallback) => {
      console.warn('Text search not fully implemented in WebC adapter');
    },
    watchPaths: (options: WatchPathsOptions, callback: (event: PathWatcherEvent) => void) => {
      if (!this._isBooted) {
        console.warn('WebC system not booted, cannot watch paths');
        return;
      }

      console.log('watchPaths called with options:', options);

      for (const includePath of options.include) {
        const basePath = includePath.replace(/\*\*.*$/, '');

        try {
          this._fs.watch(basePath, (event: PathWatcherEvent) => {
            console.log('watchPaths event', event);

            if (options.exclude) {
              for (const excludePattern of options.exclude) {
                if (event.path.includes(excludePattern.replace(/\*\*/g, ''))) {
                  return;
                }
              }
            }

            callback(event);
          });
        } catch (error) {
          console.warn(`Failed to watch path: ${basePath}`, error);
        }
      }
    },
  };
}
