import { convertToCoreMessages, streamText as _streamText, type Message } from 'ai';
import { MAX_TOKENS, type FileMap } from './constants';
import { getSystemPrompt } from '~/lib/common/prompts/prompts';
import { DEFAULT_MODEL, DEFAULT_PROVIDER, MODIFICATIONS_TAG_NAME, PROVIDER_LIST, WORK_DIR } from '~/utils/constants';
import type { IProviderSetting } from '~/types/model';
import { PromptLibrary } from '~/lib/common/prompt-library';
import { allowedHTMLElements } from '~/utils/markdown';
import { LLMManager } from '~/lib/modules/llm/manager';
import { createScopedLogger } from '~/utils/logger';
import { createFilesContext, extractPropertiesFromMessage } from './utils';
import { discussPrompt } from '~/lib/common/prompts/discuss-prompt';
import type { DesignScheme } from '~/types/design-scheme';

export type Messages = Message[];

export interface StreamingOptions extends Omit<Parameters<typeof _streamText>[0], 'model'> {
  supabaseConnection?: {
    isConnected: boolean;
    hasSelectedProject: boolean;
    credentials?: {
      anonKey?: string;
      supabaseUrl?: string;
    };
  };
}

const logger = createScopedLogger('stream-text');

function sanitizeText(text: string): string {
  let sanitized = text.replace(/<div class=\\"__boltThought__\\">.*?<\/div>/s, '');
  sanitized = sanitized.replace(/<think>.*?<\/think>/s, '');
  sanitized = sanitized.replace(/<boltAction type="file" filePath="package-lock\.json">[\s\S]*?<\/boltAction>/g, '');

  return sanitized.trim();
}

export async function streamText(props: {
  messages: Omit<Message, 'id'>[];
  env?: Env;
  options?: StreamingOptions;
  apiKeys?: Record<string, string>;
  files?: FileMap;
  providerSettings?: Record<string, IProviderSetting>;
  promptId?: string;
  contextOptimization?: boolean;
  contextFiles?: FileMap;
  summary?: string;
  messageSliceId?: number;
  chatMode?: 'discuss' | 'build';
  designScheme?: DesignScheme;
}) {
  // 📋 记录函数开始和输入参数
  logger.info('🚀 streamText function started');
  logger.info('📥 Input parameters:', {
    messagesCount: props.messages?.length || 0,
    hasEnv: !!props.env,
    hasOptions: !!props.options,
    apiKeysProvided: props.apiKeys ? Object.keys(props.apiKeys) : [],
    filesCount: props.files ? Object.keys(props.files).length : 0,
    providerSettingsKeys: props.providerSettings ? Object.keys(props.providerSettings) : [],
    promptId: props.promptId || 'default',
    contextOptimization: props.contextOptimization,
    contextFilesCount: props.contextFiles ? Object.keys(props.contextFiles).length : 0,
    hasSummary: !!props.summary,
    messageSliceId: props.messageSliceId,
    chatMode: props.chatMode || 'build',
    hasDesignScheme: !!props.designScheme,
  });

  const {
    messages,
    env: serverEnv,
    options,
    apiKeys,
    files,
    providerSettings,
    promptId,
    contextOptimization,
    contextFiles,
    summary,
    chatMode,
    designScheme,
  } = props;

  let currentModel = DEFAULT_MODEL;
  let currentProvider = DEFAULT_PROVIDER.name;

  // 🔄 记录消息处理开始
  logger.info('🔄 Starting message processing');
  logger.debug('📝 Original messages:', messages.map((msg, index) => ({
    index,
    role: msg.role,
    contentLength: typeof msg.content === 'string' ? msg.content.length : 'non-string',
    hasParts: Array.isArray(msg.parts)
  })));

  let processedMessages = messages.map((message, index) => {
    const newMessage = { ...message };

    if (message.role === 'user') {
      try {
        const { model, provider, content } = extractPropertiesFromMessage(message);
        logger.debug(`📨 Processing user message ${index}:`, {
          extractedModel: model,
          extractedProvider: provider,
          originalContentLength: typeof message.content === 'string' ? message.content.length : 'non-string',
          sanitizedContentLength: sanitizeText(content).length
        });
        currentModel = model;
        currentProvider = provider;
        newMessage.content = sanitizeText(content);
      } catch (error: unknown) {
        logger.error(`❌ Error processing user message ${index}:`, error);
        throw error;
      }
    } else if (message.role == 'assistant') {
      const originalLength = typeof message.content === 'string' ? message.content.length : 0;
      newMessage.content = sanitizeText(message.content);
      logger.debug(`🤖 Processing assistant message ${index}:`, {
        originalLength,
        sanitizedLength: newMessage.content.length
      });
    }

    // Sanitize all text parts in parts array, if present
    if (Array.isArray(message.parts)) {
      logger.debug(`🧩 Processing ${message.parts.length} parts for message ${index}`);
      newMessage.parts = message.parts.map((part) =>
        part.type === 'text' ? { ...part, text: sanitizeText(part.text) } : part,
      );
    }

    return newMessage;
  });

  logger.info('✅ Message processing completed', {
    processedMessagesCount: processedMessages.length,
    selectedModel: currentModel,
    selectedProvider: currentProvider
  });

  // 🎯 记录提供商和模型选择过程
  logger.info('🎯 Finding provider and model');
  const provider = PROVIDER_LIST.find((p) => p.name === currentProvider) || DEFAULT_PROVIDER;

  if (provider !== PROVIDER_LIST.find((p) => p.name === currentProvider)) {
    logger.warn('⚠️ Provider not found, using default provider:', {
      requestedProvider: currentProvider,
      defaultProvider: DEFAULT_PROVIDER.name
    });
  }

  logger.info('🔍 Getting static models from provider:', {
    providerName: provider.name,
    providerConfig: provider.config
  });

  const staticModels = LLMManager.getInstance().getStaticModelListFromProvider(provider);
  let modelDetails = staticModels.find((m) => m.name === currentModel);

  logger.debug('📊 Static models available:', {
    staticModelsCount: staticModels.length,
    staticModelNames: staticModels.map(m => m.name),
    requestedModel: currentModel,
    modelFoundInStatic: !!modelDetails
  });

  if (!modelDetails) {
    logger.info('🔄 Model not found in static list, fetching dynamic models');

    try {
      const dynamicModels = await LLMManager.getInstance().getModelListFromProvider(provider, {
        apiKeys,
        providerSettings,
        serverEnv: serverEnv as any,
      });

      const modelsList = [
        ...(provider.staticModels || []),
        ...dynamicModels,
      ];

      logger.info('📋 Combined models list:', {
        staticModelsCount: provider.staticModels?.length || 0,
        dynamicModelsCount: dynamicModels.length,
        totalModelsCount: modelsList.length,
        allModelNames: modelsList.map(m => m.name)
      });

      if (!modelsList.length) {
        const errorMsg = `No models found for provider ${provider.name}`;
        logger.error('❌ No models available:', {
          providerName: provider.name,
          hasApiKeys: !!apiKeys,
          hasProviderSettings: !!providerSettings,
          hasServerEnv: !!serverEnv
        });
        throw new Error(errorMsg);
      }

      modelDetails = modelsList.find((m) => m.name === currentModel);

      if (!modelDetails) {
        // Fallback to first model
        logger.warn(
          `⚠️ MODEL [${currentModel}] not found in provider [${provider.name}]. Falling back to first model: ${modelsList[0].name}`,
          {
            requestedModel: currentModel,
            availableModels: modelsList.map(m => m.name),
            fallbackModel: modelsList[0].name
          }
        );
        modelDetails = modelsList[0];
      } else {
        logger.info('✅ Model found in dynamic list:', {
          modelName: modelDetails.name,
          modelMaxTokens: modelDetails.maxTokenAllowed
        });
      }
    } catch (error: unknown) {
      logger.error('❌ Error fetching dynamic models:', {
        error: error instanceof Error ? error.message : String(error),
        providerName: provider.name,
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  } else {
    logger.info('✅ Model found in static list:', {
      modelName: modelDetails.name,
      modelMaxTokens: modelDetails.maxTokenAllowed
    });
  }

  const dynamicMaxTokens = modelDetails && modelDetails.maxTokenAllowed ? modelDetails.maxTokenAllowed : MAX_TOKENS;
  logger.info('🎛️ Token configuration:', {
    modelName: modelDetails.name,
    modelMaxTokenAllowed: modelDetails.maxTokenAllowed,
    defaultMaxTokens: MAX_TOKENS,
    finalMaxTokens: dynamicMaxTokens
  });

  // 📝 记录系统提示构建过程
  logger.info('📝 Building system prompt');

  let systemPrompt;
  try {
    systemPrompt = PromptLibrary.getPropmtFromLibrary(promptId || 'default', {
      cwd: WORK_DIR,
      allowedHtmlElements: allowedHTMLElements,
      modificationTagName: MODIFICATIONS_TAG_NAME,
      designScheme,
      supabase: {
        isConnected: options?.supabaseConnection?.isConnected || false,
        hasSelectedProject: options?.supabaseConnection?.hasSelectedProject || false,
        credentials: options?.supabaseConnection?.credentials || undefined,
      },
    }) ?? getSystemPrompt();

    logger.debug('📋 System prompt configuration:', {
      promptId: promptId || 'default',
      workDir: WORK_DIR,
      hasDesignScheme: !!designScheme,
      supabaseConfig: {
        isConnected: options?.supabaseConnection?.isConnected || false,
        hasSelectedProject: options?.supabaseConnection?.hasSelectedProject || false,
        hasCredentials: !!options?.supabaseConnection?.credentials
      },
      systemPromptLength: systemPrompt.length
    });
  } catch (error: unknown) {
    logger.error('❌ Error building system prompt:', error);
    throw error;
  }

  // 🗂️ 记录上下文文件处理
  if (chatMode === 'build' && contextFiles && contextOptimization) {
    logger.info('🗂️ Processing context files for build mode');

    try {
      const codeContext = createFilesContext(contextFiles, true);
      const originalSystemPromptLength = systemPrompt.length;

      systemPrompt = `${systemPrompt}

    Below is the artifact containing the context loaded into context buffer for you to have knowledge of and might need changes to fullfill current user request.
    CONTEXT BUFFER:
    ---
    ${codeContext}
    ---
    `;

      logger.info('📁 Context files processed:', {
        contextFilesCount: Object.keys(contextFiles).length,
        codeContextLength: codeContext.length,
        originalPromptLength: originalSystemPromptLength,
        newPromptLength: systemPrompt.length
      });

      if (summary) {
        logger.info('📄 Adding chat summary to prompt');
        const summaryLength = props.summary?.length || 0;

        systemPrompt = `${systemPrompt}
      below is the chat history till now
      CHAT SUMMARY:
      ---
      ${props.summary}
      ---
      `;

        logger.debug('📊 Summary configuration:', {
          summaryLength,
          messageSliceId: props.messageSliceId,
          originalMessagesCount: processedMessages.length
        });

        if (props.messageSliceId) {
          const originalCount = processedMessages.length;
          processedMessages = processedMessages.slice(props.messageSliceId);
          logger.info('✂️ Messages sliced:', {
            originalCount,
            sliceId: props.messageSliceId,
            newCount: processedMessages.length
          });
        } else {
          const lastMessage = processedMessages.pop();
          if (lastMessage) {
            processedMessages = [lastMessage];
            logger.info('📝 Using only last message:', {
              messageRole: lastMessage.role,
              messageContentLength: typeof lastMessage.content === 'string' ? lastMessage.content.length : 'non-string'
            });
          }
        }
      }
    } catch (error: unknown) {
      logger.error('❌ Error processing context files:', error);
      throw error;
    }
  } else {
    logger.info('ℹ️ Skipping context processing:', {
      chatMode,
      hasContextFiles: !!contextFiles,
      contextOptimization
    });
  }

  // 🔒 记录文件锁定处理
  const effectiveLockedFilePaths = new Set<string>();

  if (files) {
    logger.info('🔒 Processing file locks');
    for (const [filePath, fileDetails] of Object.entries(files)) {
      if (fileDetails?.isLocked) {
        effectiveLockedFilePaths.add(filePath);
        logger.debug('🔐 File locked:', filePath);
      }
    }
  }

  if (effectiveLockedFilePaths.size > 0) {
    const lockedFilesListString = Array.from(effectiveLockedFilePaths)
      .map((filePath) => `- ${filePath}`)
      .join('\n');

    const originalPromptLength = systemPrompt.length;
    systemPrompt = `${systemPrompt}

    IMPORTANT: The following files are locked and MUST NOT be modified in any way. Do not suggest or make any changes to these files. You can proceed with the request but DO NOT make any changes to these files specifically:
    ${lockedFilesListString}
    ---
    `;

    logger.info('🔒 Locked files added to prompt:', {
      lockedFilesCount: effectiveLockedFilePaths.size,
      lockedFiles: Array.from(effectiveLockedFilePaths),
      originalPromptLength,
      newPromptLength: systemPrompt.length
    });
  } else {
    logger.info('🔓 No locked files found');
  }

  // 🚀 记录最终调用参数
  const finalSystemPrompt = chatMode === 'build' ? systemPrompt : discussPrompt();
  const finalMessages = convertToCoreMessages(processedMessages as any);

  logger.info('🚀 Preparing final LLM call:', {
    providerName: provider.name,
    modelName: modelDetails.name,
    maxTokens: dynamicMaxTokens,
    chatMode,
    systemPromptLength: finalSystemPrompt.length,
    messagesCount: finalMessages.length,
    hasOptions: !!options
  });

  logger.debug('📤 Final call parameters:', {
    systemPromptPreview: finalSystemPrompt.substring(0, 200) + '...',
    messagesPreview: finalMessages.map((msg, index) => ({
      index,
      role: msg.role,
      contentLength: typeof msg.content === 'string' ? msg.content.length : 'array'
    })),
    optionsKeys: options ? Object.keys(options) : []
  });

  try {
    logger.info('🎯 Calling _streamText...');
    const result = await _streamText({
      model: provider.getModelInstance({
        model: modelDetails.name,
        serverEnv,
        apiKeys,
        providerSettings,
      }),
      system: finalSystemPrompt,
      maxTokens: dynamicMaxTokens,
      messages: finalMessages,
      ...options,
    });

    logger.info('✅ _streamText call successful');
    return result;
  } catch (error: unknown) {
    logger.error('❌ Error in _streamText call:', {
      error: error instanceof Error ? error.message : String(error),
      errorName: error instanceof Error ? error.name : 'Unknown',
      stack: error instanceof Error ? error.stack : undefined,
      providerName: provider.name,
      modelName: modelDetails.name
    });
    throw error;
  }
}
