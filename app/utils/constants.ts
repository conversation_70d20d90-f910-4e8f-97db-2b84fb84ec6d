import { LLMManager } from '~/lib/modules/llm/manager';
import type { Template } from '~/types/template';

export const HOME_DIR_NAME = 'web_user';
export const HOME_DIR = `/home/<USER>
export const NPM_PREFIX_DIR = `${HOME_DIR}/.npm-global`;

export const WORK_DIR_NAME = 'project';
export const WORK_DIR = `/home/<USER>
export const MODIFICATIONS_TAG_NAME = 'bolt_file_modifications';
export const MODEL_REGEX = /^\[Model: (.*?)\]\n\n/;
export const PROVIDER_REGEX = /\[Provider: (.*?)\]\n\n/;
export const DEFAULT_MODEL = 'qwen3-coder-plus';
export const PROMPT_COOKIE_KEY = 'cachedPrompt';
export const TOOL_EXECUTION_APPROVAL = {
  APPROVE: 'Yes, approved.',
  REJECT: 'No, rejected.',
} as const;
export const TOOL_NO_EXECUTE_FUNCTION = 'Error: No execute function found on tool';
export const TOOL_EXECUTION_DENIED = 'Error: User denied access to tool execution';
export const TOOL_EXECUTION_ERROR = 'Error: An error occured while calling tool';

const llmManager = LLMManager.getInstance(import.meta.env);

export const PROVIDER_LIST = llmManager.getAllProviders();
export const DEFAULT_PROVIDER = llmManager.getDefaultProvider();

export const providerBaseUrlEnvKeys: Record<string, { baseUrlKey?: string; apiTokenKey?: string }> = {};
PROVIDER_LIST.forEach((provider) => {
  providerBaseUrlEnvKeys[provider.name] = {
    baseUrlKey: provider.config.baseUrlKey,
    apiTokenKey: provider.config.apiTokenKey,
  };
});

// starter Templates

/**
 * 模版切换开关
 *
 * 设置为 true 时：
 * - 跳过 LLM 模版选择调用，直接使用固定的 vite-react-typescript 模版
 * - 大幅提升响应速度，减少网络依赖
 * - 适合大多数 React + TypeScript 项目需求
 *
 * 设置为 false 时：
 * - 使用原有的 LLM 智能模版选择逻辑
 * - 支持多种模版类型（Angular、Vue、Astro 等）
 * - 需要调用 LLM 接口进行模版推荐
 *
 * 建议：开发环境设置为 true，生产环境可根据需求调整
 */
export const USE_FIXED_VITE_TEMPLATE = true;

export const STARTER_TEMPLATES: Template[] = [
  // {
  //   name: 'Expo App',
  //   label: 'Expo App',
  //   description: 'Expo starter template for building cross-platform mobile apps',
  //   githubRepo: 'xKevIsDev/bolt-expo-template',
  //   tags: ['mobile', 'expo', 'mobile-app', 'android', 'iphone'],
  //   icon: 'i-bolt:expo',
  // },
  // {
  //   name: 'Basic Astro',
  //   label: 'Astro Basic',
  //   description: 'Lightweight Astro starter template for building fast static websites',
  //   githubRepo: 'xKevIsDev/bolt-astro-basic-template',
  //   tags: ['astro', 'blog', 'performance'],
  //   icon: 'i-bolt:astro',
  // },
  // {
  //   name: 'NextJS Shadcn',
  //   label: 'Next.js with shadcn/ui',
  //   description: 'Next.js starter fullstack template integrated with shadcn/ui components and styling system',
  //   githubRepo: 'xKevIsDev/bolt-nextjs-shadcn-template',
  //   tags: ['nextjs', 'react', 'typescript', 'shadcn', 'tailwind'],
  //   icon: 'i-bolt:nextjs',
  // },
  // {
  //   name: 'Vite Shadcn',
  //   label: 'Vite with shadcn/ui',
  //   description: 'Vite starter fullstack template integrated with shadcn/ui components and styling system',
  //   githubRepo: 'xKevIsDev/vite-shadcn',
  //   tags: ['vite', 'react', 'typescript', 'shadcn', 'tailwind'],
  //   icon: 'i-bolt:shadcn',
  // },
  // {
  //   name: 'Qwik Typescript',
  //   label: 'Qwik TypeScript',
  //   description: 'Qwik framework starter with TypeScript for building resumable applications',
  //   githubRepo: 'xKevIsDev/bolt-qwik-ts-template',
  //   tags: ['qwik', 'typescript', 'performance', 'resumable'],
  //   icon: 'i-bolt:qwik',
  // },
  // {
  //   name: 'Remix Typescript',
  //   label: 'Remix TypeScript',
  //   description: 'Remix framework starter with TypeScript for full-stack web applications',
  //   githubRepo: 'xKevIsDev/bolt-remix-ts-template',
  //   tags: ['remix', 'typescript', 'fullstack', 'react'],
  //   icon: 'i-bolt:remix',
  // },
  // {
  //   name: 'Slidev',
  //   label: 'Slidev Presentation',
  //   description: 'Slidev starter template for creating developer-friendly presentations using Markdown',
  //   githubRepo: 'xKevIsDev/bolt-slidev-template',
  //   tags: ['slidev', 'presentation', 'markdown'],
  //   icon: 'i-bolt:slidev',
  // },
  // {
  //   name: 'Sveltekit',
  //   label: 'SvelteKit',
  //   description: 'SvelteKit starter template for building fast, efficient web applications',
  //   githubRepo: 'bolt-sveltekit-template',
  //   tags: ['svelte', 'sveltekit', 'typescript'],
  //   icon: 'i-bolt:svelte',
  // },
  // {
  //   name: 'Vanilla Vite',
  //   label: 'Vanilla + Vite',
  //   description: 'Minimal Vite starter template for vanilla JavaScript projects',
  //   githubRepo: 'xKevIsDev/vanilla-vite-template',
  //   tags: ['vite', 'vanilla-js', 'minimal'],
  //   icon: 'i-bolt:vite',
  // },
  {
    name: 'Vite React',
    label: 'React + Vite + typescript',
    description: 'React starter template powered by Vite for fast development experience',
    githubRepo: 'xKevIsDev/bolt-vite-react-ts-template',
    tags: ['react', 'vite', 'frontend', 'website', 'app'],
    icon: 'i-bolt:react',
  },
  // {
  //   name: 'Vite Typescript',
  //   label: 'Vite + TypeScript',
  //   description: 'Vite starter template with TypeScript configuration for type-safe development',
  //   githubRepo: 'xKevIsDev/bolt-vite-ts-template',
  //   tags: ['vite', 'typescript', 'minimal'],
  //   icon: 'i-bolt:typescript',
  // },
  // {
  //   name: 'Vue',
  //   label: 'Vue.js',
  //   description: 'Vue.js starter template with modern tooling and best practices',
  //   githubRepo: 'xKevIsDev/bolt-vue-template',
  //   tags: ['vue', 'typescript', 'frontend'],
  //   icon: 'i-bolt:vue',
  // },
  // {
  //   name: 'Angular',
  //   label: 'Angular Starter',
  //   description: 'A modern Angular starter template with TypeScript support and best practices configuration',
  //   githubRepo: 'xKevIsDev/bolt-angular-template',
  //   tags: ['angular', 'typescript', 'frontend', 'spa'],
  //   icon: 'i-bolt:angular',
  // },
  // {
  //   name: 'SolidJS',
  //   label: 'SolidJS Tailwind',
  //   description: 'Lightweight SolidJS starter template for building fast static websites',
  //   githubRepo: 'xKevIsDev/solidjs-ts-tw',
  //   tags: ['solidjs'],
  //   icon: 'i-bolt:solidjs',
  // },
];
