export function withResolvers<T>(): PromiseWithResolvers<T> {
  if (typeof Promise.withResolvers === 'function') {
    return Promise.withResolvers();
  }

  let resolve!: (value: T | PromiseLike<T>) => void;
  let reject!: (reason?: any) => void;

  const promise = new Promise<T>((_resolve, _reject) => {
    resolve = _resolve;
    reject = _reject;
  });

  return {
    resolve,
    reject,
    promise,
  };
}

export class Deferred<T> {
  promise: Promise<T>;

  resolve: (value: T | PromiseLike<T>) => void;

  reject: (reason?: any) => void;

  constructor() {
    const { promise, resolve, reject } = withResolvers<T>();
    this.promise = promise;
    this.resolve = resolve;
    this.reject = reject;
  }
}

export function isPromisePending(promise: Promise<any>): Promise<boolean> {
  const t = {};
  return Promise.race([promise, t]).then(
    (v) => v === t,
    () => false,
  );
}
