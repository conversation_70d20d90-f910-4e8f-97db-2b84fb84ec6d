import type { LoaderFunction } from '@remix-run/node';
import { json } from '@remix-run/node';
import { LLMManager } from '~/lib/modules/llm/manager';
import { getApiKeysFromCookie } from '~/lib/api/cookies';
import { createScopedLogger } from '~/utils/logger';

const logger = createScopedLogger('api.check-env-key');

export const loader: LoaderFunction = async ({ context, request }) => {
  try {
    logger.info('🚀 API check-env-key started');

    const url = new URL(request.url);
    const provider = url.searchParams.get('provider');

    // 安全地访问 cloudflare env
    const cloudflareEnv = (context as any)?.cloudflare?.env;

    logger.info('📥 Request details:', {
      url: request.url,
      method: request.method,
      provider: provider,
      hasContext: !!context,
      hasCloudflareEnv: !!cloudflareEnv,
    });

    if (!provider) {
      logger.warn('⚠️ No provider specified in query parameters');
      return json({ isSet: false });
    }

    logger.info('🔧 Creating LLM Manager instance');
    let llmManager;
    try {
      llmManager = LLMManager.getInstance(cloudflareEnv);
      logger.info('✅ LLM Manager created successfully');
    } catch (error: unknown) {
      logger.error('❌ Failed to create LLM Manager:', {
        error: error instanceof Error ? error.message : String(error),
        hasEnv: !!cloudflareEnv,
        envKeys: cloudflareEnv ? Object.keys(cloudflareEnv) : []
      });
      return json({ isSet: false, error: 'Failed to initialize LLM Manager' });
    }

    logger.info('🔍 Getting provider instance:', { providerName: provider });
    const providerInstance = llmManager.getProvider(provider);

    if (!providerInstance) {
      logger.warn('⚠️ Provider not found:', {
        requestedProvider: provider,
        availableProviders: llmManager.getAllProviders().map(p => p.name)
      });
      return json({ isSet: false });
    }

    logger.info('✅ Provider found:', {
      providerName: providerInstance.name,
      hasConfig: !!providerInstance.config,
      configKeys: providerInstance.config ? Object.keys(providerInstance.config) : []
    });

    if (!providerInstance.config.apiTokenKey) {
      logger.warn('⚠️ Provider has no API token key configured:', {
        providerName: providerInstance.name,
        config: providerInstance.config
      });
      return json({ isSet: false });
    }

    const envVarName = providerInstance.config.apiTokenKey;
    logger.info('🔑 Checking API key:', { envVarName });

    // Get API keys from cookie
    logger.info('🍪 Getting API keys from cookies');
    const cookieHeader = request.headers.get('Cookie');
    let apiKeys;
    try {
      apiKeys = getApiKeysFromCookie(cookieHeader);
      logger.debug('🍪 Cookie API keys:', {
        hasCookieHeader: !!cookieHeader,
        cookieLength: cookieHeader?.length || 0,
        apiKeysFound: apiKeys ? Object.keys(apiKeys) : [],
        hasProviderKey: !!(apiKeys?.[provider])
      });
    } catch (error: unknown) {
      logger.error('❌ Failed to get API keys from cookies:', {
        error: error instanceof Error ? error.message : String(error),
        cookieHeader: cookieHeader ? `[${cookieHeader.length} chars]` : 'none'
      });
      apiKeys = {};
    }

    /*
     * Check API key in order of precedence:
     * 1. Client-side API keys (from cookies)
     * 2. Server environment variables (from Cloudflare env)
     * 3. Process environment variables (from .env.local)
     * 4. LLMManager environment variables
     */

    // Check each source
    const cookieKey = apiKeys?.[provider];
    const cloudflareEnvKey = cloudflareEnv?.[envVarName];
    const processEnvKey = process.env[envVarName];
    const llmManagerEnvKey = llmManager.env[envVarName];

    // 安全预览函数，避免泄露 API key
    const safePreview = (key: string | undefined) => {
      if (!key) return 'none';
      if (key.length <= 8) return '[masked]';
      return `${key.substring(0, 4)}...${key.substring(key.length - 4)}`;
    };

    logger.info('🔍 API key source check:', {
      envVarName,
      sources: {
        cookie: {
          exists: !!cookieKey,
          length: cookieKey ? cookieKey.length : 0,
          preview: safePreview(cookieKey)
        },
        cloudflareEnv: {
          exists: !!cloudflareEnvKey,
          length: cloudflareEnvKey ? cloudflareEnvKey.length : 0,
          preview: safePreview(cloudflareEnvKey)
        },
        processEnv: {
          exists: !!processEnvKey,
          length: processEnvKey ? processEnvKey.length : 0,
          preview: safePreview(processEnvKey)
        },
        llmManagerEnv: {
          exists: !!llmManagerEnvKey,
          length: llmManagerEnvKey ? llmManagerEnvKey.length : 0,
          preview: safePreview(llmManagerEnvKey)
        }
      }
    });

    const isSet = !!(
      cookieKey ||
      cloudflareEnvKey ||
      processEnvKey ||
      llmManagerEnvKey
    );

    const result = { isSet };

    console.log('process.version', process.version);
    console.log('Response', Response);

    logger.info('📤 Final result:', {
      isSet,
      foundInSource: isSet ? (
        cookieKey ? 'cookie' :
        cloudflareEnvKey ? 'cloudflareEnv' :
        processEnvKey ? 'processEnv' :
        llmManagerEnvKey ? 'llmManagerEnv' : 'unknown'
      ) : 'none'
    });

    return json(result);

  } catch (error: unknown) {
    logger.error('❌ Unexpected error in check-env-key API:', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      requestUrl: request.url
    });

    return json({
      isSet: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    });
  }
};
