/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
// Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
// SPDX-License-Identifier: MIT

import "./src/env.js";
import createNextIntlPlugin from 'next-intl/plugin';
import getPublicPath from './webpack-utils/getPublicPath.js';

const withNextIntl = createNextIntlPlugin('./src/i18n.ts');

// Get CDN public path for production builds
const cdnPublicPath = getPublicPath();

const config = {
  // Enable static export for static deployment
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  },
  
  // Configure asset prefix for CDN deployment
  // This adds the CDN URL prefix to all static assets
  assetPrefix: cdnPublicPath || undefined,

  // Disable type checking during build (types are checked separately)
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },

  // Set environment variable for static website mode
  // env: {
  //   NEXT_PUBLIC_STATIC_WEBSITE_ONLY: 'true'
  // },

  // Enable experimental features for better dev experience
  experimental: {
    // Enable optimized compilation
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  // Development server configuration
  ...(process.env.NODE_ENV === 'development' && {
    // Enable faster builds in development
    reactStrictMode: true,
    // Optimize for development
    onDemandEntries: {
      // Period (in ms) where the server will keep pages in the buffer
      maxInactiveAge: 25 * 1000,
      // Number of pages that should be kept simultaneously without being disposed
      pagesBufferLength: 2,
    },
  }),

  // For development mode
  turbopack: {
    rules: {
      "*.md": {
        loaders: ["raw-loader"],
        as: "*.js",
      },
    },
  },

  // For production mode
  webpack: (config, { isServer, dev }) => {
    // Development-specific webpack configuration
    if (dev) {
      // Enable hot module replacement
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
      };

      // Improve development performance
      config.devtool = 'eval-cheap-module-source-map';

      // Enable fast refresh
      config.resolve.alias = {
        ...config.resolve.alias,
        'react-refresh/runtime': require.resolve('react-refresh/runtime'),
      };
    }
    // Add markdown loader
    config.module.rules.push({
      test: /\.md$/,
      use: "raw-loader",
    });

    // Remove hash from static asset filenames for client-side builds
    // This enables overwrite-style CDN deployment with version control through build platform
    if (!isServer) {
      // Set public path for CDN deployment
      if (cdnPublicPath) {
        config.output.publicPath = `${cdnPublicPath}/_next/`;
      }

      // JavaScript files - remove contenthash from filenames
      config.output.filename = 'static/js/[name].js';
      config.output.chunkFilename = 'static/chunks/[name].js';

      // Static assets (images, fonts, etc.) - remove hash from filenames
      config.output.assetModuleFilename = 'static/media/[name][ext]';

      // Add specific rules for static assets to ensure no hash in filenames
      // This handles fonts, images, and other static resources
      config.module.rules.push({
        test: /\.(png|jpe?g|gif|svg|woff2?|eot|ttf|otf|webp|ico)$/i,
        type: 'asset/resource',
        generator: {
          filename: 'static/media/[name][ext]'
        }
      });
    }

    return config;
  },
};

export default withNextIntl(config);
