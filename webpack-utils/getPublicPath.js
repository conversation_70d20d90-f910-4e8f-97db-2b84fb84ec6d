/**
 * Get CDN public path for static assets
 * Based on build environment variables
 */

/**
 * Parse command line arguments from BUILD_ARGV_STR
 * Simple parser to avoid external dependencies
 * @param {string} argStr - The argument string
 * @returns {{ [key: string]: string }} Parsed arguments
 */
function parseArguments(argStr) {
  /** @type {{ [key: string]: string }} */
  const args = {};
  if (!argStr) return args;

  // Split by spaces, handling quoted values
  const parts = argStr.match(/--\S+=[^\s]+/g) || [];

  parts.forEach(part => {
    const [key, value] = part.split('=');
    if (key && value) {
      // Remove -- prefix and convert to camelCase
      const cleanKey = key.replace(/^--/, '').replace(/-/g, '_');
      args[cleanKey] = value;
    }
  });

  return args;
}

/**
 * Get the CDN public path based on build environment
 * @returns {string} CDN public path
 */
function getPublicPath() {
  // Default to root path for local development
  let publicPath = '';

  const {
    BUILD_ARGV_STR,
    BUILD_GIT_GROUP,
    BUILD_GIT_PROJECT,
    BUILD_GIT_BRANCH,
  } = process.env;

  // Parse build arguments if available
  if (BUILD_ARGV_STR) {
    try {
      const buildArgv = parseArguments(BUILD_ARGV_STR);
      const publishType = buildArgv.def_publish_type;
      const publishEnv = buildArgv.def_publish_env;

      // Only set CDN path when publish type is specified
      if (publishType && BUILD_GIT_GROUP && BUILD_GIT_PROJECT && BUILD_GIT_BRANCH) {
        // Determine CDN host based on environment
        // daily/dev environment uses dev.g.alicdn.com
        // production uses g.alicdn.com
        const cdnHost = publishEnv === 'daily' ? 'https://dev.g.alicdn.com' : 'https://g.alicdn.com';

        // Extract version from branch name (e.g., daily/0.0.1 -> 0.0.1)
        const version = BUILD_GIT_BRANCH.split('/')[1] || '0.0.1';

        // Build the complete CDN path
        // Format: https://[cdn-host]/[group]/[project]/[version]/
        publicPath = `${cdnHost}/${BUILD_GIT_GROUP}/${BUILD_GIT_PROJECT}/${version}`;
      }
    } catch (error) {
      console.warn('Failed to parse build arguments:', error);
    }
  }

  // For debugging purposes
  if (process.env.NODE_ENV === 'development') {
    console.log('PublicPath configuration:', {
      BUILD_ARGV_STR,
      BUILD_GIT_GROUP,
      BUILD_GIT_PROJECT,
      BUILD_GIT_BRANCH,
      publicPath
    });
  }

  return publicPath;
}

export default getPublicPath;
